import { Body, Controller, Get, Post, Req, Res, UseGuards } from '@nestjs/common';
import { TagsService } from './tags.service';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  @Post()
  @UseGuards(AuthGuard)
  async create(@Body() body: { name: string; background_color?:string,text_color?:string }, @Req() req: any, @Res() res: any) {
    return this.tagsService.createTag(body, req, res);
  }

  @Get()
  @UseGuards(AuthGuard)
  async list(@Req() req: any, @Res() res: any) {
    return this.tagsService.listTags(req, res);
  }
}


