import { Body, Controller, Delete, Get, NotFoundException, Param, Post, Put, Query, Req, Res, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { MetaOnboardingService } from './meta-onboarding.service';
import { CreateMetaCredentialsDto } from 'src/dto/meta-credentials.dto';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('meta-onboarding')
export class MetaOnboardingController {
    constructor(private readonly metaOnboardingService: MetaOnboardingService) {}

    @Post('connect')
    @UseGuards(AuthGuard)
    async connectPhone(@Body() createMetaCredentialsDto: CreateMetaCredentialsDto, @Req() req: any, @Res() res: Response) {
        return await this.metaOnboardingService.createMetaCredentials(createMetaCredentialsDto, req, res);
    }

    @Get('credentials')
    @UseGuards(AuthGuard)
    async getCredentials(@Req() req: any, @Query() query: any, @Res() res: Response) {
        // Get pagination params from query string, with defaults
        const page = parseInt(query?.page) > 0 ? parseInt(query.page) : 1;
        const limit = parseInt(query?.limit) > 0 ? parseInt(query.limit) : 10;
        const skip = (page - 1) * limit;
        return await this.metaOnboardingService.getMetaCredentialsByUser(req, res, skip, limit);
    }

    @Get('workspace/:workspaceId/credentials')
    @UseGuards(AuthGuard)
    async getCredentialsByWorkspace(@Param('workspaceId') workspaceId: string, @Req() req: any, @Query() query: any, @Res() res: Response) {
        // Get pagination params from query string, with defaults
        const page = parseInt(query?.page) > 0 ? parseInt(query.page) : 1;
        const limit = parseInt(query?.limit) > 0 ? parseInt(query.limit) : 10;
        const skip = (page - 1) * limit;
        return await this.metaOnboardingService.getMetaCredentialsByWorkspace(workspaceId, req, res, skip, limit);
    }
}
