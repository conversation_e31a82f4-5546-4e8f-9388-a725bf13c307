import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, <PERSON><PERSON>, Req, Re<PERSON> } from '@nestjs/common';
import { WorkspaceService } from './workspace.service';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { AuthGuard } from '../auth/auth.guard';
import { Response } from 'express';
import { CreateMemberdto } from 'src/dto/workspaceMemeber.dto';

@Controller('workspace')
@UseGuards(AuthGuard)
export class WorkspaceController {
  private readonly logger = new Logger(WorkspaceController.name);

  constructor(private readonly workspaceService: WorkspaceService) {}

  @Post()
  async create(@Body() createWorkspaceDto: CreateWorkspaceDto, @Req() req: any, @Res() res: Response) {
    this.logger.log('Creating workspace');
    return await this.workspaceService.create(createWorkspaceDto, req, res);
  }
  @Post('add-member')
  async addMember(@Body() memberData: CreateMemberdto, @Req() req: any, @Res() res: Response) {
    this.logger.log('Adding member to workspace');
    return await this.workspaceService.addMemberToWorkspace(memberData, req, res);
  }

  @Get('members')
  async getWorkspaceMembers(@Req() req: any, @Res() res: Response) {
    this.logger.log('Getting workspace members');
    return await this.workspaceService.getWorkspaceMembers(req, res);
  }
  
} 