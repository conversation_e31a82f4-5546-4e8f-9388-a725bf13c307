import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

export interface MetaTemplateRequest {
  name: string;
  category: string;
  components: MetaTemplateComponent[];
  language: string;
}

export interface MetaTemplateComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
  format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT' | 'LOCATION';
  text?: string;
  add_security_recommendation?: boolean;
  code_expiration_minutes?: number;
  example?: {
    header_text?: string[];
    header_handle?: string[];
    body_text?: string[][];
  };
  buttons?: MetaTemplateButton[];
}

export interface MetaTemplateButton {
  type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER' | 'COPY_CODE' | 'OTP' | 'CATALOG' | 'MPM';
  text: string;
  url?: string;
  phone_number?: string;
  otp_type?: 'COPY_CODE' | 'ONE_TAP';
  autofill_text?: string;
  package_name?: string;
  signature_hash?: string;
}

export interface MetaTemplateResponse {
  id: string;
  status: string;
  category: string;
  components: MetaTemplateComponent[];
  language: string;
  name: string;
  quality_rating?: string;
  quality_score?: number;
}

export interface MetaTemplateListResponse {
  data: MetaTemplateResponse[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
  };
}

@Injectable()
export class MetaApiService {
  private readonly logger = new Logger(MetaApiService.name);
  private readonly axiosInstance: AxiosInstance;
  private readonly baseUrl = 'https://graph.facebook.com/v19.0';

  constructor(private configService: ConfigService) {
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
    });
  }

  /**
   * Create a WhatsApp template using Meta's API
   */
  async createTemplate(
    whatsappBusinessId: string,
    accessToken: string,
    templateData: MetaTemplateRequest
  ): Promise<MetaTemplateResponse> {
    try {
      // Meta API endpoint for creating templates - uses WhatsApp Business Account ID
      const url = `/${whatsappBusinessId}/message_templates`;
      
      // Prepare the request payload according to Meta's API specification
      const payload = {
        name: templateData.name,
        category: templateData.category,
        components: templateData.components,
        language: templateData.language
      };
      this.logger.log(`Creating template in Meta: ${JSON.stringify(payload)}`);
      
      const response = await this.axiosInstance.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      this.logger.log(`Template created successfully in Meta: ${response.data.id}`);
      return response.data;
    } catch (error) {
      
      this.logger.error('Failed to create Meta template:', error.response?.data || error.message);
      throw new BadRequestException(error);
    }
  }

  /**
   * Get all templates for a WhatsApp Business Account
   */
  async getTemplates(
    whatsappBusinessId: string,
    accessToken: string
  ): Promise<MetaTemplateListResponse> {
    try {
      const url = `/${whatsappBusinessId}/message_templates`;
      
      const response = await this.axiosInstance.get(url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        }
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get Meta templates:', error.response?.data || error.message);
      throw new BadRequestException(`Meta API error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(
    templateId: string,
    accessToken: string
  ): Promise<MetaTemplateResponse> {
    try {
      const url = `/${templateId}`;
      
      const response = await this.axiosInstance.get(url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get Meta template:', error.response?.data || error.message);
      throw new BadRequestException(`Meta API error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Update a template
   */
  async updateTemplate(
    templateId: string,
    accessToken: string,
    templateData: Partial<MetaTemplateRequest>
  ): Promise<MetaTemplateResponse> {
    try {
      const url = `/${templateId}`;
      
      const response = await this.axiosInstance.post(url, templateData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      this.logger.log(`Template updated successfully: ${templateId}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to update Meta template:', error.response?.data || error.message);
      throw new BadRequestException(`Meta API error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Delete a template
   */
  async deleteTemplate(
    templateName: string,
    whatsappBusinessId: string,
    accessToken: string
  ): Promise<void> {
    try {
      const url = `/${whatsappBusinessId}/message_templates?name=${templateName}`;
      await this.axiosInstance.delete(url, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
    } catch (error) {
      // Throw error to propagate to controller
      throw error;
    }
  }
  
  
  

  /**
   * Get template categories
   */
  async getTemplateCategories(accessToken: string): Promise<any> {
    try {
      const url = '/template_categories';
      
      const response = await this.axiosInstance.get(url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get template categories:', error.response?.data || error.message);
      throw new BadRequestException(`Meta API error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Send a template message via WhatsApp Business API
   */
  async sendTemplateMessage(
    phoneNumberId: string,
    templateName: string,
    recipientPhoneNumber: string,
    countryCode: string,
    variables: Record<string, any> = {},
    language: string = 'en',
    accessToken: string
  ): Promise<any> {
    try {
      const url = `/${phoneNumberId}/messages`;
      
      // Prepare the message payload according to Meta API specification
      const payload: any = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: countryCode ? countryCode + recipientPhoneNumber: recipientPhoneNumber,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: language
          },
          components: []
        }
      };
      console.log("payload",payload)
              // Add variables if provided
      if (Object.keys(variables).length > 0) {
        const components: any[] = [];
        
        // Add body variables
        if (variables.body) {
          const bodyParameters: any[] = [];
          
          // Convert object entries to array and sort by key to maintain order
          const sortedEntries = Object.entries(variables.body).sort(([a], [b]) => {
            // Extract numbers from keys like "{{1}}", "{{2}}" etc.
            const numA = parseInt(a.replace(/\D/g, ''));
            const numB = parseInt(b.replace(/\D/g, ''));
            return numA - numB;
          });
          
          for (const [key, value] of sortedEntries) {
            if (value !== null && typeof value === 'string') {
              bodyParameters.push({
                type: 'text',
                text: value
              });
            } else if (value !== null && typeof value === 'object' && value !== null && 'type' in value) {
              // Handle complex parameter types (currency, date_time, etc.)
              bodyParameters.push(value);
            }
          }
          
          if (bodyParameters.length > 0) {
            components.push({
              type: 'body',
              parameters: bodyParameters
            });
          }
        }

        // Add header variables
        if (variables.header) {
          const headerParameters: any[] = [];
          
          // Convert object entries to array and sort by key to maintain order
          const sortedEntries = Object.entries(variables.header).sort(([a], [b]) => {
            // Extract numbers from keys like "{{1}}", "{{2}}" etc.
            const numA = parseInt(a.replace(/\D/g, ''));
            const numB = parseInt(b.replace(/\D/g, ''));
            return numA - numB;
          });
          
          for (const [key, value] of sortedEntries) {
            if (value !== null && typeof value === 'string') {
              headerParameters.push({
                type: 'text',
                text: value
              });
            } else if (value !== null && typeof value === 'object' && value !== null && 'type' in value) {
              // Handle complex parameter types (currency, date_time, etc.)
              headerParameters.push(value);
            }
          }
          
          if (headerParameters.length > 0) {
            components.push({
              type: 'header',
              parameters: headerParameters
            });
          }
        }

        // Add button variables
        if (variables.button) {
          const buttonParameters: any[] = [];
          
          // Convert object entries to array and sort by key to maintain order
          const sortedEntries = Object.entries(variables.button).sort(([a], [b]) => {
            // Extract numbers from keys like "{{1}}", "{{2}}" etc.
            const numA = parseInt(a.replace(/\D/g, ''));
            const numB = parseInt(b.replace(/\D/g, ''));
            return numA - numB;
          });
          
          for (const [key, value] of sortedEntries) {
            if (value !== null && typeof value === 'string') {
              buttonParameters.push({
                type: 'text',
                text: value
              });
            } else if (value !== null && typeof value === 'object' && value !== null && 'type' in value) {
              buttonParameters.push(value);
            }
          }
          
          if (buttonParameters.length > 0) {
            components.push({
              type: 'button',
              sub_type: 'quick_reply',
              index: 0,
              parameters: buttonParameters
            });
          }
        }

        payload.template.components = components;
      }

      // Make the actual API call to Meta
      this.logger.log(`Sending template message to ${recipientPhoneNumber} with template ${templateName}`);
      this.logger.log('Payload:', JSON.stringify(payload, null, 2));
      const response = await this.axiosInstance.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      this.logger.log(`Template message sent successfully: ${response.data.messages?.[0]?.id}`);
      return {
        success: true,
        message_id: response.data.messages?.[0]?.id,
        payload:payload,
        response:response.data
        
      };

    } catch (error) {
      this.logger.error('Failed to send template message:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Convert our template format to Meta's format
   */
  convertToMetaFormat(template: any): MetaTemplateRequest {
    // this.logger.log(`Converting template to Meta format: ${JSON.stringify(template, null, 2)}`);
    console.log("template payload for meta conversion",template)
 
    
    const components: MetaTemplateComponent[] = [];

    // Add header if image/video/document/text/location
    if (template.type === 'image' && template.imageUrl) {
      components.push({
        type: 'HEADER',
        format: 'IMAGE',
        example: {
          header_handle: [template.imageUrl] // For image headers, use header_handle
        }
      });
    } else if (template.type === 'video' && template.imageUrl) {
      components.push({
        type: 'HEADER',
        format: 'VIDEO',
        example: {
          header_handle: [template.imageUrl] // For video headers, use header_handle
        }
      });
    } else if (template.type === 'document' && template.imageUrl) {
      components.push({
        type: 'HEADER',
        format: 'DOCUMENT',
        example: {
          header_handle: [template.imageUrl] // For document headers, use header_handle
        }
      });
    } else if (template.type === 'location') {
      components.push({
        type: 'HEADER',
        format: 'LOCATION'
      });
    } else if (template.headerText) {
      // Add text header with variables
      const headerText = template.headerText;
      const headerExamples: string[] = [];
      
      // Extract header variables if they exist
      if (template.variables) {
        const headerVariableKeys = Object.keys(template.variables)
          .filter(key => key.startsWith('header_') && key.endsWith('}}'))
          .sort((a, b) => {
            const numA = parseInt(a.replace(/\D/g, ''));
            const numB = parseInt(b.replace(/\D/g, ''));
            return numA - numB;
          });
        
        for (const key of headerVariableKeys) {
          const exampleValue = template.variables[`header_variable${key.replace(/\D/g, '')}`] || 'Example';
          headerExamples.push(exampleValue);
        }
      }
      
      components.push({
        type: 'HEADER',
        format: 'TEXT',
        text: headerText,
        example: {
          header_text: headerExamples.length > 0 ? headerExamples : [headerText]
        }
      });
    }

    // Add body with proper variable handling
    if (template.content) {
      const bodyExamples: string[][] = [];
      const exampleValues: string[] = [];

      if (template.variables) {
        this.logger.log(`Processing variables: ${JSON.stringify(template.variables, null, 2)}`);

        // Extract variables in order ({{1}}, {{2}}, etc.)
        const variableKeys = Object.keys(template.variables)
          .filter(key => key.startsWith('{{') && key.endsWith('}}'))
          .sort((a, b) => {
            const numA = parseInt(a.replace(/\D/g, ''));
            const numB = parseInt(b.replace(/\D/g, ''));
            return numA - numB;
          });

        this.logger.log(`Sorted variable keys: ${JSON.stringify(variableKeys, null, 2)}`);

        // Collect all example values into a single array
        for (const key of variableKeys) {
          const variableName = template.variables[key];
          const exampleValue = variableName; // Use the value directly
          exampleValues.push(exampleValue);
          this.logger.log(`Variable ${key}: ${variableName} -> Example: ${exampleValue}`);
        }
      }

      // Add the single array of examples into the bodyExamples
      if (exampleValues.length > 0) {
        bodyExamples.push(exampleValues);
      }
      
      console.log("bodyExamples", bodyExamples);
      this.logger.log(`Body examples: ${JSON.stringify(bodyExamples, null, 2)}`);

      const bodyComponent: MetaTemplateComponent = {
        type: 'BODY',
        text: template.content
      };

      // Add security recommendation for authentication templates
      if (template.category === 'AUTHENTICATION') {
        bodyComponent.add_security_recommendation = true;
      }
      
      // Add example if variables exist
      if (bodyExamples.length > 0) {
        bodyComponent.example = {
          body_text: bodyExamples
        };
      }
      
      console.log("bodyComponent", bodyComponent);
      components.push(bodyComponent);
    }

    // Add footer if provided
    if (template.footer) {
      const footerComponent: MetaTemplateComponent = {
        type: 'FOOTER',
        text: template.footer
      };

      // Add code expiration for authentication templates
      if (template.category === 'AUTHENTICATION' && template.codeExpirationMinutes) {
        footerComponent.code_expiration_minutes = template.codeExpirationMinutes;
      }

      components.push(footerComponent);
    }

          // Add buttons for interactive templates
    if (template.buttons && template.buttons.length > 0) {
      const buttons: MetaTemplateButton[] = template.buttons.map((btn: any, index: number) => {
        const button: MetaTemplateButton = {
          type: 'QUICK_REPLY',
          text: btn.title || btn.text
        };

        // Handle different button types
        if (btn.type === 'URL' || btn.url) {
          button.type = 'URL';
          button.url = btn.url;
        } else if (btn.type === 'PHONE_NUMBER' || btn.phone_number) {
          button.type = 'PHONE_NUMBER';
          button.phone_number = btn.phone_number;
        } else if (btn.type === 'OTP') {
          button.type = 'OTP';
          button.otp_type = btn.otp_type || 'COPY_CODE';
          if (btn.otp_type === 'ONE_TAP') {
            button.autofill_text = btn.autofill_text;
            button.package_name = btn.package_name;
            button.signature_hash = btn.signature_hash;
          }
        } else if (btn.type === 'CATALOG') {
          button.type = 'CATALOG';
        } else if (btn.type === 'MPM') {
          button.type = 'MPM';
        }

        return button;
      });

      components.push({
        type: 'BUTTONS',
        buttons
      });
    }

    const result = {
      name: template.name,
      category: template.category,
      components,
      language: template.language
    };
    
    this.logger.log(`Final Meta template format: ${JSON.stringify(result, null, 2)}`);
    return result;
  }

  /**
   * Convert Meta's format to our format
   */
  convertFromMetaFormat(metaTemplate: MetaTemplateResponse): any {
    const template: any = {
      id: metaTemplate.id,
      name: metaTemplate.name,
      category: metaTemplate.category,
      language: metaTemplate.language,
      status: metaTemplate.status,
      quality_rating: metaTemplate.quality_rating,
      quality_score: metaTemplate.quality_score,
      components: metaTemplate.components
    };

    // Extract content from components
    const bodyComponent = metaTemplate.components.find(c => c.type === 'BODY');
    if (bodyComponent) {
      template.content = bodyComponent.text;
    }

    // Extract footer
    const footerComponent = metaTemplate.components.find(c => c.type === 'FOOTER');
    if (footerComponent) {
      template.footer = footerComponent.text;
    }

    // Extract buttons
    const buttonsComponent = metaTemplate.components.find(c => c.type === 'BUTTONS');
    if (buttonsComponent && buttonsComponent.buttons) {
      template.buttons = buttonsComponent.buttons.map(btn => ({
        id: btn.text.toLowerCase().replace(/\s+/g, '_'),
        title: btn.text,
        type: btn.type,
        url: btn.url,
        phone_number: btn.phone_number
      }));
    }

    // Extract header (image/video/document/text)
    const headerComponent = metaTemplate.components.find(c => c.type === 'HEADER');
    if (headerComponent) {
      template.type = headerComponent.format?.toLowerCase() || 'text';
      if (headerComponent.format === 'TEXT') {
        template.headerText = headerComponent.text;
      } else if (headerComponent.example?.header_text) {
        template.imageCaption = headerComponent.example.header_text[0];
      }
    }

    return template;
  }

  /**
   * Send a regular message (text, image, video, etc.) to a contact
   */
  async sendMessage(
    phoneNumberId: string,
    contactPhone: string,
    type: 'text' | 'image' | 'video' | 'audio' | 'document',
    content: {
      text?: string;
      media_url?: string;
      caption?: string;
      filename?: string;
    },
    countryCode: string = '+1',
    accessToken: string,
  ): Promise<{ success: boolean; message_id?: string; error?: string }> {
    try {
      const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;
      
      let messageData: any = {
        messaging_product: 'whatsapp',
        to: `${countryCode}${contactPhone}`,
        type: type,
      };

      switch (type) {
        case 'text':
          messageData.text = {
            body: content.text || '',
          };
          break;

        case 'image':
          messageData.image = {
            link: content.media_url,
            caption: content.caption || '',
          };
          break;

        case 'video':
          messageData.video = {
            link: content.media_url,
            caption: content.caption || '',
          };
          break;

        case 'audio':
          messageData.audio = {
            link: content.media_url,
          };
          break;

        case 'document':
          messageData.document = {
            link: content.media_url,
            filename: content.filename || 'document',
            caption: content.caption || '',
          };
          break;

        default:
          throw new BadRequestException(`Unsupported message type: ${type}`);
      }

      const response = await this.axiosInstance.post(url, messageData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data && response.data.messages && response.data.messages[0]) {
        return {
          success: true,
          message_id: response.data.messages[0].id,
        };
      } else {
        return {
          success: false,
          error: 'Invalid response from Meta API',
        };
      }
    } catch (error) {
      this.logger.error('Failed to send message via Meta API:', error);
      
      let errorMessage = 'Failed to send message';
      if (error.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }
} 