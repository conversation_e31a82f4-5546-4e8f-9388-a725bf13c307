import { <PERSON><PERSON><PERSON>y, IsBoolean, Is<PERSON>num, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { CustomFieldTypeEnum } from '../../schema/custom-field.schema';

export class CreateCustomFieldDto {
    @IsString({ message: 'label must be a string' })
    @IsNotEmpty({ message: 'label is required' })
    label!: string;

    @IsEnum(CustomFieldTypeEnum, { message: 'type must be one of text, number, date, datetime, dropdown, bool' })
    type!: CustomFieldTypeEnum;

    @IsArray({ message: 'options must be an array of strings' })
    @IsOptional()
    options?: string[];

    @IsBoolean({ message: 'showOnContact must be a boolean' })
    @IsOptional()
    showOnContact?: boolean;

    @IsBoolean({ message: 'showOnChat must be a boolean' })
    @IsOptional()
    showOnChat?: boolean;
}


