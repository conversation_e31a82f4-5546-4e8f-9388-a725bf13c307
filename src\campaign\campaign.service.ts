import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Response } from 'express';
import { Campaign, CampaignDocument } from 'src/schema/campaign.schema';
import { Contact, ContactDocument } from 'src/schema/contacts.schema';
import {
  CreateCampaignDto,
  UpdateCampaignDto,
  CampaignQueryDto,
  SendTestMessageDto,
  CampaignActionDto,
  CampaignStatsDto,
} from 'src/dto/campaign.dto';
import { SupabaseService } from 'src/supabase/supabase.service';
import { MetaApiService } from 'src/meta-api/meta-api.service';
import { TemplateService } from 'src/template/template.service';
import { CampaignProcessorService } from './campaign-processor.service';

@Injectable()
export class CampaignService {
  private readonly logger = new Logger(CampaignService.name);

  constructor(
    @InjectModel(Campaign.name) private campaignModel: Model<CampaignDocument>,
    @InjectModel(Contact.name) private contactModel: Model<ContactDocument>,
    private readonly supabaseService: SupabaseService,
    private readonly metaApiService: MetaApiService,
    private readonly templateService: TemplateService,
    private readonly campaignProcessorService: CampaignProcessorService,
  ) {}

  async createCampaign(
    createCampaignDto: CreateCampaignDto,
    req: any,
    res: Response,
  ) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    // Get user profile to get workspace_id
    const userProfile = await this.supabaseService.getUserProfile(user.id);
    if (userProfile.error || !userProfile.data) {
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: 'User profile not found',
        timestamp: new Date().toISOString(),
      });
    }

    try {
      // Validate template exists using direct Supabase query
      const { data: template, error: templateError } =
        await this.supabaseService
          .getClient()
          .from('automate_whatsapp_templates')
          .select('*')
          .eq('id', createCampaignDto.template_id)
          .single();

      if (templateError || !template) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Template not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Get contacts count
      const contacts = await this.getCampaignContacts(
        createCampaignDto,
        userProfile.data.workspace_id,
      );
      console.log("contacts",contacts)
      const totalContacts = contacts.length;
      if (totalContacts == 0) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'No contacts found',
          timestamp: new Date().toISOString(),
        });
      }
      // Create campaign
      const campaign = new this.campaignModel({
        ...createCampaignDto,
        created_by: user.id, // Keep as string (UUID)
        workspace_id: parseInt(userProfile.data.workspace_id), // Convert to number (bigint)
        template_id: createCampaignDto.template_id, // Keep as string (UUID)
        contact_selection_type: createCampaignDto.contact_selection_type,
        csv_contacts_string: createCampaignDto.csv_contacts_string,
        csv_mapping: createCampaignDto.csv_mapping,
        total_contacts: totalContacts,
        pending_count: totalContacts,
        delivery_stats: {
          sent: 0,
          delivered: 0,
          read: 0,
          failed: 0,
          pending: totalContacts,
          total: totalContacts,
        },
        message_logs: contacts.map((contact: any) => ({
          contact_id: contact._id || null,
          phone: contact.phoneNumber,
          status: 'PENDING',
          retry_count: 0,
        })),
      });
      const savedCampaign = await campaign.save();

      return res.status(201).json({
        status: 'success',
        code: 201,
        message: 'Campaign created successfully',
        data: savedCampaign,
        summary: {
          total_contacts: totalContacts,
          can_send_test: true,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to create campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to create campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async getCampaigns(query: CampaignQueryDto, req: any, res: Response) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    const userProfile = await this.supabaseService.getUserProfile(user.id);
    if (userProfile.error || !userProfile.data) {
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: 'User profile not found',
        timestamp: new Date().toISOString(),
      });
    }

    try {
      const page = query.page || 1;
      const limit = query.limit || 10;
      const skip = (page - 1) * limit;

      const filterQuery: any = {
        workspace_id: parseInt(userProfile.data.workspace_id), // Convert to number (bigint)
        is_deleted: false,
      };

      if (query.status) {
        filterQuery.status = query.status;
      }

      if (query.send_type) {
        filterQuery.send_type = query.send_type;
      }

      if (query.search) {
        filterQuery.$or = [
          { name: { $regex: query.search, $options: 'i' } },
          { description: { $regex: query.search, $options: 'i' } },
        ];
      }

      const sortQuery: any = {};
      if (query.sort_by) {
        sortQuery[query.sort_by] = query.sort_order === 'desc' ? -1 : 1;
      } else {
        sortQuery.createdAt = -1;
      }

      const campaigns = await this.campaignModel
        .find(filterQuery)
        .sort(sortQuery)
        .skip(skip)
        .limit(limit)
        .lean();

      const total = await this.campaignModel.countDocuments(filterQuery);

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaigns retrieved successfully',
        data: campaigns,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to get campaigns:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to get campaigns: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async getCampaignById(id: string, req: any, res: Response) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    const userProfile = await this.supabaseService.getUserProfile(user.id);
    if (userProfile.error || !userProfile.data) {
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: 'User profile not found',
        timestamp: new Date().toISOString(),
      });
    }

    try {
      const campaign = await this.campaignModel
        .findOne({
          _id: id,
          workspace_id: parseInt(userProfile.data.workspace_id),
          is_deleted: false,
        })
        .lean();

      if (!campaign) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Campaign not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Add contact selection details
      const campaignWithDetails = {
        ...campaign,
        contact_selection_info: this.getContactSelectionInfo(campaign),
      };

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign retrieved successfully',
        data: campaignWithDetails,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to get campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to get campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async updateCampaign(
    campaignId: string,
    updateCampaignDto: UpdateCampaignDto,
    req: any,
    res: Response,
  ) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    try {
      const campaign = await this.campaignModel.findById(campaignId);
      if (!campaign) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Campaign not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Only allow updates if campaign is in DRAFT status
      if (campaign.status !== 'DRAFT') {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Campaign can only be updated when in DRAFT status',
          timestamp: new Date().toISOString(),
        });
      }

      const updatedCampaign = await this.campaignModel.findByIdAndUpdate(
        campaignId,
        {
          ...updateCampaignDto,
          template_id: updateCampaignDto.template_id, // Keep as string (UUID)
        },
        { new: true },
      );

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign updated successfully',
        data: updatedCampaign,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to update campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to update campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async deleteCampaign(campaignId: string, req: any, res: Response) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    try {
      const campaign = await this.campaignModel.findById(campaignId);
      if (!campaign) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Campaign not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Only allow deletion if campaign is in DRAFT status
      if (campaign.status !== 'DRAFT') {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Campaign can only be deleted when in DRAFT status',
          timestamp: new Date().toISOString(),
        });
      }

      await this.campaignModel.findByIdAndUpdate(campaignId, {
        is_deleted: true,
      });

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign deleted successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to delete campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to delete campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async sendTestMessage(
    sendTestMessageDto: SendTestMessageDto,
    req: any,
    res: Response,
  ) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    try {
      const { data: template, error: templateError } =
        await this.supabaseService
          .getClient()
          .from('automate_whatsapp_templates')
          .select('*')
          .eq('id', sendTestMessageDto.template_id)
          .single();

      if (templateError || !template) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Template not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Check if template is approved by Meta
      if (!template.meta_template_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message:
            'Template is not approved by Meta. Please get it approved first.',
          timestamp: new Date().toISOString(),
        });
      }

      if (template.meta_template_status !== 'APPROVED') {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: `Template status is ${template.meta_template_status}. Only APPROVED templates can be used for sending messages.`,
          timestamp: new Date().toISOString(),
        });
      }

      // Get user profile to get workspace_id
      const userProfile = await this.supabaseService.getUserProfile(user.id);
      if (userProfile.error || !userProfile.data) {
        throw new BadRequestException('User profile not found');
      }

      // Get Meta credentials for the workspace
      const { data: metaCredentials, error: credentialsError } =
        await this.supabaseService
          .getClient()
          .from('automate_whatsapp_meta_credentials')
          .select('*')
          .eq('phone_number_id', sendTestMessageDto.phone_number_id)
          .eq('status', 'Active')
          .single();

      if (!metaCredentials || credentialsError) {
        throw new BadRequestException(
          'Meta credentials not found or inactive. Please configure your WhatsApp Business API credentials.',
        );
      }

      const results: any[] = [];
      for (const phone of sendTestMessageDto.test_contacts) {
        const result = await this.metaApiService.sendTemplateMessage(
          sendTestMessageDto.phone_number_id,
          template.name,
          phone,
          sendTestMessageDto.country_code,
          sendTestMessageDto.variable_mapping,
          template.language,
          metaCredentials.access_token,
        );
        results.push(result);
      }

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Test messages sent successfully',
        data: {
          results,
          test_contacts: sendTestMessageDto.test_contacts,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to send test messages:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to send test messages: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async executeCampaignAction(
    campaignId: string,
    campaignActionDto: CampaignActionDto,
    req: any,
    res: Response,
  ) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }
    console.log('campaignActionDto', campaignActionDto);
    try {
      const campaign = await this.campaignModel.findById(campaignId);
      if (!campaign) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Campaign not found',
          timestamp: new Date().toISOString(),
        });
      }
      switch (campaignActionDto.action) {
        case 'START':
          return await this.startCampaign(campaign, res);
        case 'PAUSE':
          return await this.pauseCampaign(campaign, res);
        case 'RESUME':
          return await this.resumeCampaign(campaign, res);
        case 'CANCEL':
          return await this.cancelCampaign(campaign, res);
        default:
          return res.status(400).json({
            status: 'error',
            code: 400,
            message: 'Invalid action',
            timestamp: new Date().toISOString(),
          });
      }
    } catch (error) {
      this.logger.error('Failed to execute campaign action:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to execute campaign action: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async getCampaignStats(
    campaignStatsDto: CampaignStatsDto,
    req: any,
    res: Response,
  ) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    try {
      const campaign = await this.campaignModel.findById(
        campaignStatsDto.campaign_id,
      );
      if (!campaign) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Campaign not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Get detailed message logs if date range is provided
      let messageLogs: any[] = [];
      if (campaignStatsDto.start_date || campaignStatsDto.end_date) {
        const dateFilter: any = {};
        if (campaignStatsDto.start_date) {
          dateFilter.$gte = new Date(campaignStatsDto.start_date);
        }
        if (campaignStatsDto.end_date) {
          dateFilter.$lte = new Date(campaignStatsDto.end_date);
        }

        messageLogs = campaign.message_logs.filter((log) => {
          if (dateFilter.$gte && log.sent_at && log.sent_at < dateFilter.$gte)
            return false;
          if (dateFilter.$lte && log.sent_at && log.sent_at > dateFilter.$lte)
            return false;
          return true;
        });
      }

      const stats = {
        campaign_id: campaign._id,
        name: campaign.name,
        status: campaign.status,
        total_contacts: campaign.total_contacts,
        delivery_stats: campaign.delivery_stats,
        progress_percentage:
          campaign.total_contacts > 0
            ? ((campaign.sent_count + campaign.failed_count) /
                campaign.total_contacts) *
              100
            : 0,
        message_logs: messageLogs.length > 0 ? messageLogs : undefined,
        created_at: new Date(),
        started_at: campaign.started_at,
        completed_at: campaign.completed_at,
      };

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign statistics retrieved successfully',
        data: stats,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to get campaign stats:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to get campaign stats: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async getCampaignCreationData(req: any, res: Response) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    try {
      const userProfile = await this.supabaseService.getUserProfile(user.id);
      if (userProfile.error || !userProfile.data) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Get segments with their conditions for the UI
      const segments = await this.supabaseService
        .getClient()
        .from('automate_whatsapp_segments')
        .select('*')
        .eq('workspace_id', userProfile.data.workspace_id);

      // Get templates for selection
      const { data: templates, error: templatesError } =
        await this.supabaseService
          .getClient()
          .from('automate_whatsapp_templates')
          .select('*')
          .eq('created_by', user.id)
          .eq('is_active', true);

      if (templatesError) {
        this.logger.error('Failed to fetch templates:', templatesError);
      }

      // Get contact fields for condition building
      const contactFields = [
        { field: 'firstName', label: 'First Name', type: 'string' },
        { field: 'lastName', label: 'Last Name', type: 'string' },
        { field: 'email', label: 'Email', type: 'string' },
        { field: 'phoneNumber', label: 'Phone Number', type: 'string' },
        { field: 'countryCode', label: 'Country Code', type: 'string' },
        { field: 'tagsId', label: 'Tags', type: 'array' },
        { field: 'subscribed', label: 'Subscribed', type: 'boolean' },
        { field: 'customFields', label: 'Custom Fields', type: 'object' },
      ];

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign creation data retrieved successfully',
        data: {
          segments: segments.data || [],
          templates: templates || [],
          contactFields,
          operators: [
            { key: 'equals', label: 'Equals', valueType: 'any' },
            { key: 'notEquals', label: 'Not equals', valueType: 'any' },
            { key: 'contains', label: 'Contains', valueType: 'string' },
            {
              key: 'notContains',
              label: 'Does not contain',
              valueType: 'string',
            },
            { key: 'startsWith', label: 'Starts with', valueType: 'string' },
            { key: 'endsWith', label: 'Ends with', valueType: 'string' },
            { key: 'regex', label: 'Regex', valueType: 'string' },
            { key: 'exists', label: 'Exists', valueType: 'boolean' },
            { key: 'in', label: 'In list', valueType: 'array' },
            { key: 'hasAnyTag', label: 'Has any tag', valueType: 'array' },
            { key: 'hasAllTags', label: 'Has all tags', valueType: 'array' },
            {
              key: 'customFieldEquals',
              label: 'Custom field equals',
              valueType: 'any',
            },
            {
              key: 'customFieldContains',
              label: 'Custom field contains',
              valueType: 'string',
            },
            {
              key: 'customFieldExists',
              label: 'Custom field exists',
              valueType: 'boolean',
            },
          ],
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to get campaign creation data:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to get campaign creation data: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async getTemplateVariables(templateId: string, req: any, res: Response) {
    try {
      const { data: template, error: templateError } =
        await this.supabaseService
          .getClient()
          .from('automate_whatsapp_templates')
          .select('*')
          .eq('id', templateId)
          .single();

      if (templateError || !template) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Template not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Extract variables from template content
      const variables = this.extractTemplateVariables(template.content);

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Template variables extracted successfully',
        data: {
          template,
          variables,
          variableMapping: {
            header: {},
            body: {},
            buttons: {},
            footer: {},
          },
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to get template variables:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to get template variables: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async getSegmentConditions(segmentId: string, req: any, res: Response) {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException('User not found');
    }

    try {
      // Get segment with its conditions from Supabase
      const { data: segment, error: segmentError } = await this.supabaseService
        .getClient()
        .from('automate_whatsapp_segments')
        .select('*')
        .eq('id', segmentId)
        .single();

      if (segmentError || !segment) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Segment not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Convert segment rules to contact filter format
      const contactFilters = this.convertSegmentRulesToContactFilters(
        segment.rules || [],
      );

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Segment conditions retrieved successfully',
        data: {
          segment,
          contactFilters,
          rules: segment.rules || [],
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to get segment conditions:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to get segment conditions: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  private convertSegmentRulesToContactFilters(rules: any[]) {
    if (!rules || rules.length === 0) {
      return {
        conditions: [],
      };
    }

    return {
      conditions: rules.map((rule) => ({
        field: rule.field,
        operator: rule.operator,
        value: rule.value,
      })),
    };
  }

  private extractTemplateVariables(content: string): string[] {
    const variableRegex = /\{\{(\d+)\}\}/g;
    const variables: string[] = [];
    let match;

    while ((match = variableRegex.exec(content)) !== null) {
      variables.push(match[1]);
    }

    return [...new Set(variables)]; // Remove duplicates
  }

  private getContactFieldValue(contact: any, field: string): string {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      return contact[parent]?.[child] || '';
    }
    return contact[field] || '';
  }

  private async getCampaignContacts(
    createCampaignDto: CreateCampaignDto,
    workspaceId: string,
  ) {
    const contacts: any[] = [];

    switch (createCampaignDto.contact_selection_type) {
      case 'all_contacts':
        if (createCampaignDto.contact_filters) {
          const filterContacts = await this.getContactsByFilters(
            createCampaignDto.contact_filters,
            workspaceId,
          );
          contacts.push(...filterContacts);
        }
        else{ 
          const allContacts = await this.contactModel
          .find({
            workspaceId: parseInt(workspaceId),
            subscribed: { $ne: false },
          })
          .lean();
         contacts.push(...allContacts);}
        break;

      case 'segmented':
        // Get contacts based on filters
        if (createCampaignDto.contact_filters) {
          const filterContacts = await this.getContactsByFilters(
            createCampaignDto.contact_filters,
            workspaceId,
          );
          contacts.push(...filterContacts);
        }
        break;

      case 'csv_contacts':
        // Parse CSV string and create temporary contact objects
        if (createCampaignDto.csv_contacts_string) {
          const csvContacts = this.parseCsvContacts(
            createCampaignDto.csv_contacts_string,
            createCampaignDto.csv_mapping,
          );
          contacts.push(...csvContacts);
        }
        break;

      case 'quick_contacts':
        // Handle quick contacts (phone numbers array)
        console.log("quick contact",createCampaignDto.quick_contacts)
        
        if (createCampaignDto.quick_contacts && Array.isArray(createCampaignDto.quick_contacts)) {
          const quickContacts = createCampaignDto.quick_contacts.map((phone: string) => ({
            phoneNumber: phone,
            countryCode: phone.startsWith('+') ? phone.substring(1, 3) : phone.substring(0, 2),
            name: `quick contact`,
            workspaceId: parseInt(workspaceId),
            subscribed: true,
            isQuickContact: true,
          }));
          contacts.push(...quickContacts);
        }
        break;

      default:
        throw new BadRequestException('Invalid contact selection type');
    }

    // Remove duplicates based on phone number
    const uniqueContacts = contacts.filter(
      (contact, index, self) =>
        index === self.findIndex((c) => c.phoneNumber === contact.phoneNumber),
    );

    // For CSV contacts, ensure no duplicate phone numbers in the same campaign
    if (createCampaignDto.contact_selection_type === 'csv_contacts') {
      const phoneNumbers = new Set<string>();
      const filteredContacts: any[] = [];

      for (const contact of uniqueContacts) {
        if (!phoneNumbers.has(contact.phoneNumber)) {
          phoneNumbers.add(contact.phoneNumber);
          filteredContacts.push(contact);
        }
      }

      return filteredContacts;
    }
    return uniqueContacts;
  }

  private parseCsvContacts(csvString: string, csvMapping?: any): any[] {
    const contacts: any[] = [];

    try {
      // Validate CSV string format
      this.validateCsvFormat(csvString, csvMapping);

      // Split CSV by lines and remove empty lines
      const lines = csvString
        .trim()
        .split('\n')
        .filter((line) => line.trim());

      // Skip header row if it exists (assuming first row might be headers)
      const dataLines = lines.slice(1);

      for (const line of dataLines) {
        const columns = line
          .split(',')
          .map((col) => col.trim().replace(/^"|"$/g, ''));

        // Use mapping if provided, otherwise use default (first column as phone)
        const phoneColumn = csvMapping?.phone_column ?? 0;
        const nameColumn = csvMapping?.name_column;
        const emailColumn = csvMapping?.email_column;
        const countryCodeColumn = csvMapping?.country_code_column;
        const customFieldMapping = csvMapping?.custom_field_mapping ?? {};

        if (columns.length > phoneColumn && columns[phoneColumn]) {
          const contact = {
            phoneNumber: columns[phoneColumn],
            firstName:
              nameColumn !== undefined && columns[nameColumn]
                ? columns[nameColumn]
                : '',
            lastName: '',
            email:
              emailColumn !== undefined && columns[emailColumn]
                ? columns[emailColumn]
                : '',
            countryCode:
              countryCodeColumn !== undefined && columns[countryCodeColumn]
                ? columns[countryCodeColumn]
                : '',
            customFields: {},
            subscribed: true,
          };

          // Add custom fields based on mapping
          Object.keys(customFieldMapping).forEach((fieldName) => {
            const columnIndex = customFieldMapping[fieldName];
            if (columns[columnIndex]) {
              contact.customFields[fieldName] = columns[columnIndex];
            }
          });

          contacts.push(contact);
        }
      }
    } catch (error) {
      this.logger.error('Error parsing CSV contacts:', error);
      throw new BadRequestException('Invalid CSV format');
    }

    return contacts;
  }

  private validateCsvFormat(csvString: string, csvMapping?: any): void {
    if (!csvString || typeof csvString !== 'string') {
      throw new BadRequestException(
        'CSV string is required and must be a string',
      );
    }

    const lines = csvString
      .trim()
      .split('\n')
      .filter((line) => line.trim());

    if (lines.length < 2) {
      throw new BadRequestException(
        'CSV must contain at least a header row and one data row',
      );
    }

    // Use mapping if provided, otherwise use default validation
    const phoneColumn = csvMapping?.phone_column ?? 0;

    // Validate that phone column index is valid
    if (phoneColumn < 0) {
      throw new BadRequestException('Phone column index must be 0 or greater');
    }

    // Validate data rows
    for (let i = 1; i < lines.length; i++) {
      const columns = lines[i].split(',').map((col) => col.trim());
      if (columns.length <= phoneColumn || !columns[phoneColumn]) {
        throw new BadRequestException(
          `Row ${i + 1} is missing phone number in column ${phoneColumn + 1}`,
        );
      }
    }
  }

  private async getContactsByFilters(filters: any, workspaceId: string) {
    const andConditions: any[] = [
      { workspaceId: parseInt(workspaceId) },
      { subscribed: { $ne: false } },
    ];

    // Apply all conditions (including tags and custom fields)
    if (filters.conditions && filters.conditions.length > 0) {
      const conditionQueries = filters.conditions.map((condition: any) =>
        this.buildConditionQuery(condition),
      );
      andConditions.push({ $and: conditionQueries });
    }

    const query =
      andConditions.length > 1 ? { $and: andConditions } : andConditions[0];
    return await this.contactModel.find(query).lean();
  }

  private buildConditionQuery(condition: any) {
    switch (condition.operator) {
      case 'equals':
        return { [condition.field]: condition.value };
      case 'notEquals':
        return { [condition.field]: { $ne: condition.value } };
      case 'contains':
        return {
          [condition.field]: { $regex: condition.value, $options: 'i' },
        };
      case 'notContains':
        return {
          [condition.field]: {
            $not: { $regex: condition.value, $options: 'i' },
          },
        };
      case 'startsWith':
        return {
          [condition.field]: { $regex: `^${condition.value}`, $options: 'i' },
        };
      case 'endsWith':
        return {
          [condition.field]: { $regex: `${condition.value}$`, $options: 'i' },
        };
      case 'regex':
        return {
          [condition.field]: { $regex: condition.value, $options: 'i' },
        };
      case 'exists':
        return { [condition.field]: { $exists: true } };
      case 'in':
        return { [condition.field]: { $in: condition.value } };
      case 'hasAnyTag':
        return { tagsId: { $in: condition.value } };
      case 'hasAllTags':
        return { tagsId: { $all: condition.value } };
      case 'customFieldEquals':
        return { [`customFields.${condition.field}`]: condition.value };
      case 'customFieldContains':
        return {
          [`customFields.${condition.field}`]: {
            $regex: condition.value,
            $options: 'i',
          },
        };
      case 'customFieldExists':
        return { [`customFields.${condition.field}`]: { $exists: true } };
      default:
        return {};
    }
  }

  // Campaign Action Methods
  private async startCampaign(campaign: any, res: Response) {
    try {
      await this.campaignProcessorService.startCampaign(
        campaign._id.toString(),
        campaign.created_by,
      );

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign started successfully',
        data: {
          campaign_id: campaign._id,
          status: 'ACTIVE',
          started_at: new Date(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to start campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to start campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  private async pauseCampaign(campaign: any, res: Response) {
    try {
      if (campaign.status !== 'ACTIVE') {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Campaign can only be paused when ACTIVE',
          timestamp: new Date().toISOString(),
        });
      }

      await this.campaignModel.findByIdAndUpdate(campaign._id, {
        status: 'PAUSED',
        paused_at: new Date(),
        is_active: false,
      });

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign paused successfully',
        data: {
          campaign_id: campaign._id,
          status: 'PAUSED',
          paused_at: new Date(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to pause campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to pause campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  private async resumeCampaign(campaign: any, res: Response) {
    try {
      if (campaign.status !== 'PAUSED') {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Campaign can only be resumed when PAUSED',
          timestamp: new Date().toISOString(),
        });
      }

      await this.campaignModel.findByIdAndUpdate(campaign._id, {
        status: 'ACTIVE',
        resumed_at: new Date(),
        is_active: true,
      });

      // Resume processing remaining contacts
      await this.campaignProcessorService.startCampaign(
        campaign._id.toString(),
        campaign.created_by,
      );

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign resumed successfully',
        data: {
          campaign_id: campaign._id,
          status: 'ACTIVE',
          resumed_at: new Date(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to resume campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to resume campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  private async cancelCampaign(campaign: any, res: Response) {
    try {
      if (!['ACTIVE', 'PAUSED', 'SCHEDULED'].includes(campaign.status)) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message:
            'Campaign can only be cancelled when ACTIVE, PAUSED, or SCHEDULED',
          timestamp: new Date().toISOString(),
        });
      }

      await this.campaignModel.findByIdAndUpdate(campaign._id, {
        status: 'CANCELLED',
        is_active: false,
      });

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Campaign cancelled successfully',
        data: {
          campaign_id: campaign._id,
          status: 'CANCELLED',
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to cancel campaign:', error);
      return res.status(400).json({
        status: 'error',
        code: 400,
        message: `Failed to cancel campaign: ${error.message}`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  private getContactSelectionInfo(campaign: any): any {
    switch (campaign.contact_selection_type) {
      case 'all_contacts':
        return {
          type: 'all_contacts',
          description: 'All subscribed contacts in the workspace',
          total_contacts: campaign.total_contacts,
        };

      case 'segmented':
        return {
          type: 'segmented',
          description: 'Contacts filtered by specific conditions',
          filters: campaign.contact_filters,
          total_contacts: campaign.total_contacts,
        };

      case 'csv_contacts':
        return {
          type: 'csv_contacts',
          description: 'Contacts from uploaded CSV file',
          csv_has_data: !!campaign.csv_contacts_string,
          total_contacts: campaign.total_contacts,
        };

      default:
        return {
          type: 'unknown',
          description: 'Unknown contact selection type',
          total_contacts: campaign.total_contacts,
        };
    }
  }
}
