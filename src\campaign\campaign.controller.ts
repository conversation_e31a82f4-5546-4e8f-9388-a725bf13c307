import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, Res, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { CampaignService } from './campaign.service';
import { CreateCampaignDto, UpdateCampaignDto, CampaignQueryDto, SendTestMessageDto, CampaignActionDto } from 'src/dto/campaign.dto';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('campaigns')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Post()
  @UseGuards(AuthGuard)
  async createCampaign(@Body() createCampaignDto: CreateCampaignDto, @Req() req: any, @Res() res: Response) {
    return await this.campaignService.createCampaign(createCampaignDto, req, res);
  }

  @Get()
  @UseGuards(AuthGuard)
  async getCampaigns(@Query() query: CampaignQueryDto, @Req() req: any, @Res() res: Response) {
    return await this.campaignService.getCampaigns(query, req, res);
  }

  @Get(':id')
  @UseGuards(AuthGuard)
  async getCampaignById(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
    return await this.campaignService.getCampaignById(id, req, res);
  }

  @Put(':id')
  @UseGuards(AuthGuard)
  async updateCampaign(@Param('id') id: string, @Body() updateCampaignDto: UpdateCampaignDto, @Req() req: any, @Res() res: Response) {
    return await this.campaignService.updateCampaign(id, updateCampaignDto, req, res);
  }

  @Delete(':id')
  @UseGuards(AuthGuard)
  async deleteCampaign(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
    return await this.campaignService.deleteCampaign(id, req, res);
  }

  @Post('/test')
  @UseGuards(AuthGuard)
  async sendTestMessage(@Body() sendTestMessageDto: SendTestMessageDto, @Req() req: any, @Res() res: Response) {
    return await this.campaignService.sendTestMessage(sendTestMessageDto, req, res);
  }

  @Post(':id/action')
  @UseGuards(AuthGuard)
  async executeCampaignAction(@Param('id') id: string, @Body() campaignActionDto: CampaignActionDto, @Req() req: any, @Res() res: Response) {
    return await this.campaignService.executeCampaignAction(id, campaignActionDto, req, res);
  }

  // @Get(':id/stats')
  // @UseGuards(AuthGuard)
  // async getCampaignStats(@Param('id') id: string, @Query() query: CampaignStatsDto, @Req() req: any, @Res() res: Response) {
  //   const { campaign_id, ...otherQuery } = query;
  //   const campaignStatsDto = { campaign_id: id, ...otherQuery };
  //   return await this.campaignService.getCampaignStats(campaignStatsDto, req, res);
  // }

  // Campaign Creation Flow Endpoints
  // @Get('creation/data')
  // @UseGuards(AuthGuard)
  // async getCampaignCreationData(@Req() req: any, @Res() res: Response) {
  //   return await this.campaignService.getCampaignCreationData(req, res);
  // }


  // @Get('template/:id/variables')
  // @UseGuards(AuthGuard)
  // async getTemplateVariables(@Param('id') templateId: string, @Req() req: any, @Res() res: Response) {
  //   return await this.campaignService.getTemplateVariables(templateId, req, res);
  // }

  // @Get('segment/:id/conditions')
  // @UseGuards(AuthGuard)
  // async getSegmentConditions(@Param('id') segmentId: string, @Req() req: any, @Res() res: Response) {
  //   return await this.campaignService.getSegmentConditions(segmentId, req, res);
  // }


}
