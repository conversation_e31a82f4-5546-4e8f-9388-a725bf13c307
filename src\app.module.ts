import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { WorkspaceModule } from './workspace/workspace.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ContactsModule } from './contacts/contacts.module';
import { RolePermissionModule } from './role-permission/role-permission.module';
import { MetaOnboardingModule } from './meta-onboarding/meta-onboarding.module';
import { WebhooksModule } from './webhooks/webhooks.module';
import { WhatsAppModule } from './whatsapp/whatsapp.module';
import { TemplateModule } from './template/template.module';
import { TagsModule } from './tags/tags.module';
import { SegmentsModule } from './segments/segments.module';
import { StorageModule } from './storage/storage.module';
import { CustomFieldsModule } from './custom-fields/custom-fields.module';
import { AiModule } from './ai/ai.module';
import { CampaignModule } from './campaign/campaign.module';
import { AutomateCardModule } from './automate-card/automate-card.module';
import { KafkaModule } from './kafka/kafka.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    DatabaseModule,
    AuthModule,
    WorkspaceModule,
    ContactsModule,
    RolePermissionModule,
    MetaOnboardingModule,
    WebhooksModule,
    WhatsAppModule,
    TemplateModule,
    TagsModule,
    SegmentsModule,
    StorageModule,
    CustomFieldsModule,
    AiModule,
    CampaignModule,
    AutomateCardModule,
    KafkaModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
