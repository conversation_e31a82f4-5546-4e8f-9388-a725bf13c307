import { Module } from '@nestjs/common';
import { AuthGuard } from './auth.guard';
import { SupabaseModule } from '../supabase/supabase.module';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';

@Module({
  imports: [SupabaseModule],
  controllers: [AuthController],
  providers: [AuthService, AuthGuard],
  exports: [AuthService, AuthGuard],
})
export class AuthModule {} 