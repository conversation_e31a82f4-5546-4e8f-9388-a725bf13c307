import { Body, Controller, Delete, Get, NotFoundException, Param, Post, Put, Query, Req, Res, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { Response } from 'express';
import { TemplateService } from './template.service';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto, SendTemplateMessageDto, CreateAiTemplateDto, CreateVoiceTemplateDto } from 'src/dto/template.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { AiService } from 'src/ai/ai.service';
import { FileInterceptor } from '@nestjs/platform-express';
@Controller('templates')
export class TemplateController {
    constructor(
        private readonly templateService: TemplateService,
        private readonly aiService: AiService
    ) {}

    @Post()
    @UseGuards(AuthGuard)
    async createTemplate(@Body() createTemplateDto: CreateTemplateDto, @Req() req: any, @Res() res: Response) {
        return await this.templateService.createTemplate(createTemplateDto, req, res);
    }
    @Post('draft')
    @UseGuards(AuthGuard)
    async createDraftTemplate(@Body() createTemplateDto: CreateTemplateDto, @Req() req: any, @Res() res: Response) {
        return await this.templateService.createDraftTemplate(createTemplateDto, req, res);
    }

    @Get()
    @UseGuards(AuthGuard)
    async getTemplates(@Req() req: any, @Query() query: TemplateQueryDto, @Res() res: Response) {
        return await this.templateService.getTemplates(req, query, res);
    }

    @Get('workspace/:workspaceId')
    @UseGuards(AuthGuard)
    async getTemplatesByWorkspace(@Param('workspaceId') workspaceId: string, @Req() req: any, @Query() query: TemplateQueryDto, @Res() res: Response) {
        return await this.templateService.getTemplatesByWorkspace(workspaceId, req, query, res);
    }

    @Get('/:id')
    @UseGuards(AuthGuard)
    async getTemplateById(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return await this.templateService.getTemplateById(id, req, res);
    }

    @Delete('/:wabaId/template/:templateId')
    @UseGuards(AuthGuard)
    async deleteTemplate(@Param('wabaId') wabaId: string, @Param('templateId') templateId: string, @Req() req: any, @Res() res: Response) {
        return await this.templateService.deleteTemplate(templateId,wabaId, req, res);
    }
    // ----------------------------------------update template----------------------------------------
   
   
    @Get('/meta/sync/waba/:wabaId')
    @UseGuards(AuthGuard)
    async syncAllWithMeta(@Param('wabaId') wabaId: string, @Req() req: any, @Res() res: Response) {
        return await this.templateService.syncAllWithMeta(wabaId, req, res);
    }
   
   
   // -------------------------------------------------------------working--------------------------------------
   
    @Put(':id')
    @UseGuards(AuthGuard)
    async updateTemplate(@Param('id') id: string, @Body() updateTemplateDto: UpdateTemplateDto, @Req() req: any, @Res() res: Response) {
        return await this.templateService.updateTemplate(id, updateTemplateDto, req, res);
    }


    @Post(':id/sync')
    @UseGuards(AuthGuard)
    async syncWithMeta(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return await this.templateService.syncWithMeta(id, req, res);
    }

  

    @Get('meta/templates')
    @UseGuards(AuthGuard)
    async getMetaTemplates(@Req() req: any, @Res() res: Response) {
        return await this.templateService.getMetaTemplates(req, res);
    }

    @Post('ai/generate')
    @UseGuards(AuthGuard)
    async generateAiTemplate(@Body() createAiTemplateDto: CreateAiTemplateDto, @Req() req: any, @Res() res: Response) {
        return await this.templateService.generateAiTemplate(createAiTemplateDto, req, res);
    }
    @Post('voice/generate')
    @UseGuards(AuthGuard)
    @UseInterceptors(FileInterceptor('audioFile', {
        limits: {
            fileSize: 10 * 1024 * 1024, // 10MB limit
        },
        fileFilter: (req, file, cb) => {
            // Accept audio files only
            if (file.mimetype.startsWith('audio/')) {
                cb(null, true);
            } else {
                cb(new Error('Only audio files are allowed'), false);
            }
        }
    }))
    async generateTemplateFromVoice(@UploadedFile() audioFile: any, @Body() body: any, @Req() req: any, @Res() res: Response) {
        return await this.templateService.generateTemplateFromVoice(audioFile, body, req, res);
    }
} 