import { Body, Controller, Delete, Get, NotFoundException, Param, Post, Put, Req, Res, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ContactsService } from './contacts.service';
import { ContactDto } from 'src/dto/contacts.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { UpdateContactDto } from 'src/dto/update-contact.dto';

@Controller('contacts')
export class ContactsController {
    constructor(private readonly contactsService: ContactsService) {}

    @Get()
    @UseGuards(AuthGuard)
    async getContacts(@Req() req: any, @Res() res: any) {
        return await this.contactsService.getContactsForWorkspace(req, res);
    }

    @Get('me')
    @UseGuards(AuthGuard)
    async getMyContacts(@Req() req: any, @Res() res: any) {
        return await this.contactsService.getContactsForUser(req, res);
    }

    @Post()
    @UseGuards(AuthGuard)
    async createContact(@Body() contactDto: ContactDto, @Req() req: Request, @Res() res: Response) {
        return await this.contactsService.createContact(contactDto, req,res);
    }

    // @Post('bulk-import')
    // @UseGuards(AuthGuard)
    // async bulkImportContacts(@Body() contactsData: { contacts: ContactDto[] }, @Req() req: any) {
    //     return await this.contactsService.bulkImportContacts(contactsData.contacts, req);
    // }

    @Delete(':id')
    async deleteContact(@Param('id') id: string) {
        const deletedContact = await this.contactsService.deleteContact(id);
        if(!deletedContact) {
            throw new NotFoundException('Contact not found');
        }
        return { message: 'Contact deleted successfully' };
    }

    @Put(':id')
    @UseGuards(AuthGuard)
    async updateContact(
        @Param('id') id: string,
        @Body() updateDto: UpdateContactDto,
        @Req() req: any,
        @Res() res: any,
    ) {
        return await this.contactsService.updateContact(id, updateDto, req, res);
    }

    @Post('import')
    @UseGuards(AuthGuard)
    @UseInterceptors(FileInterceptor('file'))
    async importContacts(
        @UploadedFile() file: any,
        @Body('mapping') mappingJson: string,
        @Req() req: any,
        @Res() res: any,
    ) {
        const mapping = mappingJson ? JSON.parse(mappingJson) : {};
        return await this.contactsService.importContactsCsv(file, mapping, req, res);
    }
}
