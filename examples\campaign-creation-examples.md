# Campaign Creation Examples

This document shows how to use the updated campaign creation API with the new simplified contact selection structure.

## Overview

The campaign creation now uses a single `contact_selection_type` field instead of multiple contact input methods. This makes the API cleaner and easier to understand.

## Contact Selection Types

1. **all_contacts**: Send to all subscribed contacts in the workspace
2. **segmented**: Send to contacts filtered by specific conditions
3. **csv_contacts**: Send to contacts from a CSV file

## Example 1: All Contacts Campaign

```json
{
  "name": "Welcome Campaign",
  "description": "Welcome message for all subscribers",
  "template_id": "template-uuid-here",
  "phone_number_id": "phone-number-uuid-here",
  "contact_selection_type": "all_contacts",
  "send_type": "IMMEDIATE",
  "variable_mapping": {
    "body": {
      "firstName": "{{firstName}}",
      "company": "{{company}}"
    }
  }
}
```

## Example 2: Segmented Campaign

```json
{
  "name": "VIP Customer Campaign",
  "description": "Special offer for VIP customers",
  "template_id": "template-uuid-here",
  "phone_number_id": "phone-number-uuid-here",
  "contact_selection_type": "segmented",
  "contact_filters": {
    "conditions": [
      {
        "field": "tags",
        "operator": "contains",
        "value": "vip"
      },
      {
        "field": "lastActivity",
        "operator": "greaterThan",
        "value": "2024-01-01"
      }
    ]
  },
  "send_type": "SCHEDULED",
  "scheduled_at": "2024-12-25T10:00:00Z",
  "variable_mapping": {
    "body": {
      "firstName": "{{firstName}}",
      "discount": "20%"
    }
  }
}
```

## Example 3: CSV Contacts Campaign

```json
{
  "name": "CSV Import Campaign",
  "description": "Campaign for contacts from CSV file",
  "template_id": "template-uuid-here",
  "phone_number_id": "phone-number-uuid-here",
  "contact_selection_type": "csv_contacts",
  "csv_contacts_string": "Name,Phone,Email,City\nJohn Doe,+1234567890,<EMAIL>,New York\nJane Smith,+0987654321,<EMAIL>,Los Angeles",
  "csv_mapping": {
    "phone_column": 1,
    "name_column": 0,
    "email_column": 2,
    "custom_field_mapping": {
      "city": 3
    }
  },
  "variable_mapping": {
    "body": {
      "1": "{{firstName}}",
      "2": "{{city}}"
    }
  },
  "send_type": "IMMEDIATE"
}
```

### CSV Mapping Explanation:

- **phone_column**: 1 (second column contains phone numbers)
- **name_column**: 0 (first column contains names)
- **email_column**: 2 (third column contains emails)
- **custom_field_mapping**: Maps "city" field to column 3

### CSV Format:
```
Name,Phone,Email,City
John Doe,+1234567890,<EMAIL>,New York
Jane Smith,+0987654321,<EMAIL>,Los Angeles
```

### Without Mapping (Default):
If no `csv_mapping` is provided, the system assumes:
- First column (index 0) = Phone number
- Second column (index 1) = Name
- Third column (index 2) = Email
- Additional columns = Custom fields (field_1, field_2, etc.)

## CSV Format Requirements

The CSV string must follow this format:

1. **Header row**: Must contain column names (first row)
2. **Phone column**: Must have a column with phone numbers (can be named "phone", "number", or "mobile")
3. **Data rows**: Each row must have at least a phone number
4. **Optional columns**: name, email, and custom fields

### Valid CSV Example:
```csv
phone,name,email,company,department
+1234567890,John Doe,<EMAIL>,ABC Corp,Marketing
+0987654321,Jane Smith,<EMAIL>,XYZ Inc,Sales
+1122334455,Bob Wilson,<EMAIL>,123 LLC,Engineering
```

### CSV Column Mapping:
- Column 1: Phone number (required)
- Column 2: First name
- Column 3: Last name  
- Column 4: Email
- Column 5+: Custom fields (accessible as `{{1}}`, `{{2}}`, etc. in templates)

## Response Format

All campaign creation requests return a response with:

```json
{
  "status": "success",
  "code": 201,
  "message": "Campaign created successfully",
  "data": {
    "_id": "campaign-id",
    "name": "Campaign Name",
    "contact_selection_type": "csv_contacts",
    "total_contacts": 150,
    "status": "DRAFT",
    // ... other campaign fields
  },
  "summary": {
    "total_contacts": 150,
    "can_send_test": true
  },
  "timestamp": "2024-12-20T10:30:00.000Z"
}
```

## Campaign Details Response

When fetching campaign details, you'll get additional contact selection information:

```json
{
  "status": "success",
  "code": 200,
  "message": "Campaign retrieved successfully",
  "data": {
    "_id": "campaign-id",
    "name": "Campaign Name",
    "contact_selection_type": "csv_contacts",
    "contact_selection_info": {
      "type": "csv_contacts",
      "description": "Contacts from uploaded CSV file",
      "csv_has_data": true,
      "total_contacts": 150
    },
    // ... other campaign fields
  },
  "timestamp": "2024-12-20T10:30:00.000Z"
}
```

## Benefits of the New Structure

1. **Simplified API**: No more confusion between `quick_contacts` and `csv_contacts`
2. **Clear Intent**: `contact_selection_type` makes it obvious what type of contact selection is being used
3. **Better Tracking**: Campaign details show exactly how contacts were selected
4. **Flexible CSV**: Frontend can handle CSV file parsing and send the string to backend
5. **No Contact Storage**: CSV contacts are not stored in the database, only used for the campaign
