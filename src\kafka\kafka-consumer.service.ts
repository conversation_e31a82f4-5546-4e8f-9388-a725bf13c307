import { SupabaseService } from 'src/supabase/supabase.service';
import { BadRequestException, Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { Kafka, Consumer, EachMessagePayload } from 'kafkajs';
import { ConfigService } from '@nestjs/config';
import { TemplateService } from '../template/template.service';
import { MetaApiService } from '../meta-api/meta-api.service';
import { MessageStatusService } from './message-status.service';
import { CampaignMessage } from './kafka.service';

@Injectable()
export class KafkaConsumerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KafkaConsumerService.name);
  private kafka: Kafka;
  private consumer: Consumer;
  private isConnected = false;
  private isProcessing = false;

  constructor(
    private configService: ConfigService,
    private messageStatusService: MessageStatusService,
    private templateService: TemplateService,
    private metaApiService: MetaApiService,
    private supabaseService: SupabaseService,
  ) {
    this.kafka = new Kafka({
      clientId: 'whatsapp-campaign-consumer',
      brokers: this.configService.get<string>('KAFKA_BROKERS', 'localhost:9092').split(','),
      retry: {
        initialRetryTime: 100,
        retries: 8,
      },
    });

    this.consumer = this.kafka.consumer({
      groupId: 'whatsapp-campaign-consumer-group',
      sessionTimeout: 30000,
      heartbeatInterval: 3000,
    });
  }

  async onModuleInit() {
    await this.connect();
    await this.subscribeToTopics();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect() {
    try {
      await this.consumer.connect();
      this.isConnected = true;
      this.logger.log('Kafka consumer connected successfully');
    } catch (error) {
      this.logger.error('Failed to connect to Kafka consumer:', error);
      throw error;
    }
  }

  private async disconnect() {
    if (this.isConnected) {
      try {
        await this.consumer.disconnect();
        this.isConnected = false;
        this.logger.log('Kafka consumer disconnected');
      } catch (error) {
        this.logger.error('Error disconnecting from Kafka consumer:', error);
      }
    }
  }

  private async subscribeToTopics() {
    try {
      // Subscribe to campaign messages
      await this.consumer.subscribe({
        topic: 'campaign-messages',
        fromBeginning: false,
      });

      // Subscribe to scheduled campaign messages
      await this.consumer.subscribe({
        topic: 'scheduled-campaign-messages',
        fromBeginning: false,
      });

      // Subscribe to retry messages
      await this.consumer.subscribe({
        topic: 'campaign-retry-messages',
        fromBeginning: false,
      });

      // Start consuming messages
      await this.consumer.run({
        eachMessage: async (payload: EachMessagePayload) => {
          await this.processMessage(payload);
        },
        autoCommit: false,
        autoCommitInterval: 5000,
        autoCommitThreshold: 100,
      });

      this.logger.log('Kafka consumer subscribed to topics successfully');
    } catch (error) {
      this.logger.error('Failed to subscribe to Kafka topics:', error);
      throw error;
    }
  }

  private async processMessage(payload: EachMessagePayload) {
    if (this.isProcessing) {
      this.logger.warn('Message processing is already in progress, skipping message');
      return;
    }

    this.isProcessing = true;

    try {
      const { topic, partition, message } = payload;
      const messageValue = message.value?.toString();
      const messageType = message.headers?.['message-type']?.toString();

      if (!messageValue) {
        this.logger.warn('Received empty message, skipping');
        return;
      }

      this.logger.debug(`Processing message from topic: ${topic}, type: ${messageType}`);

      switch (messageType) {
        case 'campaign-message':
          await this.processCampaignMessage(JSON.parse(messageValue));
          break;
        case 'scheduled-campaign-message':
          await this.processScheduledCampaignMessage(JSON.parse(messageValue));
          break;
        case 'retry-campaign-message':
          await this.processRetryMessage(JSON.parse(messageValue));
          break;
        default:
          this.logger.warn(`Unknown message type: ${messageType}`);
      }

      // Commit the message offset
      await this.consumer.commitOffsets([
        { topic, partition, offset: (Number(message.offset) + 1).toString() },
      ]);

    } catch (error) {
      this.logger.error('Error processing message:', error);
      // Don't commit offset on error to allow retry
    } finally {
      this.isProcessing = false;
    }
  }

  private async processCampaignMessage(message: CampaignMessage) {
    try {
      this.logger.log(`Processing campaign message for campaign ${message.campaignId} to ${message.phoneNumber}`);
      const { data: template, error: templateError } =
      await this.supabaseService
        .getClient()
        .from('automate_whatsapp_templates')
        .select('*')
        .eq('id', message.templateId)
        .single();

    if (templateError || !template) {
      throw new BadRequestException("Template not found")
    }
      const { data: metaCredentials, error: credentialsError } =
      await this.supabaseService
        .getClient()
        .from('automate_whatsapp_meta_credentials')
        .select('*')
        .eq('phone_number_id', message.phoneNumberId)
        .eq('status', 'Active')
        .single();

    if (!metaCredentials || credentialsError) {
       throw new BadRequestException("Meta credentials not found or inactive. Please configure your WhatsApp Business API credentials")
    }


      // Send the template message
      const result = await this.metaApiService.sendTemplateMessage(
        message.phoneNumberId,
        template.name,
        message.phoneNumber,
        message.countryCode,
        message.variableMapping,
        template.language,
        metaCredentials.access_token,
      );
      // // Update campaign statistics - handle the response properly

      const messageId = result?.message_id
      console.log("messageId",messageId)
      // await this.updateCampaignStats(message.campaignId, message.contactId, 'SENT', messageId);

      this.logger.log(`Successfully sent campaign message to ${message.phoneNumber}`);

    } catch (error) {
      this.logger.error(`Failed to process campaign message: ${error.message}`, error.stack);
      
      // Update campaign statistics with error
      // await this.updateCampaignStats(message.campaignId, message.phoneNumber, 'FAILED', undefined, error.message);

      // Handle retry logic
      if (message.retryCount < 3) {
        await this.handleRetry(message);
      }
    }
  }

  private async processScheduledCampaignMessage(message: CampaignMessage) {
    try {
      // Check if it's time to send the scheduled message
      if (message.scheduledAt && new Date() < new Date(message.scheduledAt)) {
        this.logger.debug(`Scheduled message not ready yet for campaign ${message.campaignId}`);
        return;
      }

      await this.processCampaignMessage(message);
    } catch (error) {
      this.logger.error(`Failed to process scheduled campaign message: ${error.message}`, error.stack);
    }
  }

  private async processRetryMessage(message: CampaignMessage) {
    try {
      this.logger.log(`Processing retry message for campaign ${message.campaignId} to ${message.phoneNumber}`);

      // Add delay before retry
      await new Promise(resolve => setTimeout(resolve, 5000 * (message.retryCount + 1)));

      await this.processCampaignMessage(message);
    } catch (error) {
      this.logger.error(`Failed to process retry message: ${error.message}`, error.stack);
      
      // Update campaign statistics with error
      await this.updateCampaignStats(message.campaignId, message.contactId, 'FAILED', undefined, error.message);
    }
  }

  private async updateCampaignStats(
    campaignId: string,
    contactId: string,
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED',
    messageId?: string,
    errorMessage?: string,
  ) {
    try {
      // Update campaign statistics using the message status service
      await this.messageStatusService.updateMessageStatus(
        campaignId,
        contactId,
        status,
        messageId,
        errorMessage,
      );
    } catch (error) {
      this.logger.error(`Failed to update campaign stats: ${error.message}`, error.stack);
    }
  }

  private async handleRetry(message: CampaignMessage) {
    try {
      const retryMessage = {
        ...message,
        retryCount: message.retryCount + 1,
      };

      // Send retry message to retry topic
      // This would typically be done through the KafkaService
      this.logger.log(`Scheduling retry for campaign ${message.campaignId} to ${message.phoneNumber}`);
      
      // You can implement retry logic here or send to a retry queue
    } catch (error) {
      this.logger.error(`Failed to handle retry: ${error.message}`, error.stack);
    }
  }
}

