import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { CreateAiTemplateDto } from 'src/dto/template.dto';
import axios from 'axios';

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);
  private readonly genAI: GoogleGenerativeAI;
  private readonly model: any;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!apiKey) {
      this.logger.error('GEMINI_API_KEY is not configured');
      throw new Error('GEMINI_API_KEY is required for AI template generation');
    }

    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
  }

  async generateTemplate(aiTemplateDto: CreateAiTemplateDto): Promise<any> {
    try {
      const prompt = this.buildPrompt(aiTemplateDto.prompt);
      
      this.logger.log('Generating template with AI...');
      
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Parse the AI response
      const template = this.parseAiResponse(text, aiTemplateDto.prompt);
      
      this.logger.log('Template generated successfully');
      return template;
    } catch (error) {
      this.logger.error('Failed to generate template with AI:', error);
      throw new BadRequestException(`AI template generation failed: ${error.message}`);
    }
  }

  private buildPrompt(userPrompt: string): string {
    return `You are an expert WhatsApp Business template creator. Create a professional WhatsApp template based on the user's request.

USER REQUEST: ${userPrompt}

WHATSAPP TEMPLATE GUIDELINES:
1. Template name should be descriptive and follow WhatsApp naming conventions (no spaces, use underscores)
2. Content should be engaging, clear, and appropriate for the business context
3. Include variables using {{1}}, {{2}}, etc. for dynamic content where appropriate
4. For interactive templates, include relevant buttons (max 3)
5. Header text should be compelling (max 60 characters)
6. Body text should be informative and actionable (max 1000 characters)
7. Footer should include call-to-action or contact info (max 60 characters)
8. Follow WhatsApp Business Policy guidelines
9. Choose appropriate template type: text, interactive, image, video, document
10. Use appropriate category: MARKETING, UTILITY, AUTHENTICATION

META TEMPLATE STRUCTURE REQUIREMENTS:
- Name: Must be unique, descriptive, and follow naming conventions
- Category: Choose from: MARKETING, UTILITY, AUTHENTICATION
- Components: HEADER (optional), BODY (required), FOOTER (optional), BUTTONS (for interactive)
- Language: Use standard language codes (en, es, fr, etc.)

Please respond with a JSON object in the following format:
{
  "name": "template_name_here",
  "description": "Brief description of the template",
  "type": "text|interactive|image|video|document",
  "content": "Main message content with {{variables}} if needed",
  "headerText": "Optional header text (max 60 chars)",
  "footer": "Optional footer text (max 60 chars)",
  "buttons": [
    {
      "id": "button_id",
      "title": "Button Text",
      "url": "https://example.com" // for URL buttons
    }
  ],
  "language": "en",
  "category": "MARKETING|UTILITY|AUTHENTICATION",
  "variables": {
    "variable1": "Example value 1",
    "variable2": "Example value 2"
  },
  "meta_template_category": "MARKETING|UTILITY|AUTHENTICATION"
}

Ensure the template is:
- Professional and brand-appropriate
- Compliant with WhatsApp Business Policy
- Engaging and action-oriented
- Suitable for the specified business context
- Optimized for the chosen template type`;
  }

  private parseAiResponse(aiResponse: string, userPrompt: string): any {
    try {
      // Extract JSON from the AI response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const templateData = JSON.parse(jsonMatch[0]);
      
      // Validate and enhance the template data
      const template = {
        name: templateData.name,
        description: templateData.description,
        type: templateData.type,
        content: templateData.content,
        headerText: templateData.headerText || null,
        footer: templateData.footer || null,
        buttons: templateData.buttons || [],
        sections: templateData.sections || [],
        language: templateData.language || 'en',
        category: templateData.category || 'MARKETING',
        variables: templateData.variables || {},
        meta_template_category: templateData.meta_template_category || 'MARKETING',
        imageUrl: null,
        imageCaption: null
      };

      // Validate content length
      if (template.content.length > 1000) {
        template.content = template.content.substring(0, 997) + '...';
      }

      if (template.headerText && template.headerText.length > 60) {
        template.headerText = template.headerText.substring(0, 57) + '...';
      }

      if (template.footer && template.footer.length > 60) {
        template.footer = template.footer.substring(0, 57) + '...';
      }

      return template;
    } catch (error) {
      this.logger.error('Failed to parse AI response:', error);
      throw new BadRequestException('Failed to parse AI-generated template');
    }
  }
  public async transcribeAudio(file: any, audioFormat?: string): Promise<string> {
    try {
      // Option 1: Use Google Speech-to-Text (requires Google Cloud credentials)
      // return await this.transcribeWithGoogleSpeechToText(file.buffer, audioFormat);
      
      // Option 2: Use OpenAI Whisper (requires OpenAI API key)
      return await this.transcribeWithOpenAIWhisper(file);
      
    } catch (error) {
      this.logger.error('Transcription failed:', error);
      throw new BadRequestException(`Audio transcription failed: ${error.message}`);
    }
  }

  private async transcribeWithOpenAIWhisper(file: any): Promise<string> {
    try {
      const openaiApiKey = this.configService.get<string>('OPENAI_API_KEY');
      if (!openaiApiKey) {
        throw new Error('OPENAI_API_KEY is required for Whisper transcription');
      }
      
      // Create form data for OpenAI Whisper API
      const formData = new FormData();
      
      // Convert the file buffer to a Blob
      const blob = new Blob([file.buffer], { type: file.mimetype });
      formData.append('file', blob, file.originalname || 'audio.mp3');
      formData.append('model', 'whisper-1');
      formData.append('response_format', 'text');

      const response = await axios.post('https://api.openai.com/v1/audio/transcriptions', formData, {
        headers: {
          'Authorization': `Bearer ${openaiApiKey}`,
          // Don't set Content-Type manually, let axios set it with boundary
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('OpenAI Whisper API error:', error.response?.data || error.message);
      throw new Error(`OpenAI Whisper transcription failed: ${error.response?.data?.error || error.message}`);
    }
  }
}
