import { Controller, Post, Body, HttpCode, HttpStatus, UseGuards, Request, Get, Res, Put } from '@nestjs/common';
import { AuthService } from './auth.service';
import { SignUpDto, SignInDto, ResetPasswordDto, RefreshTokenDto, UpdateProfileDto, ChangePasswordDto } from '../dto/auth.dto';
import { AuthGuard } from './auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  @HttpCode(HttpStatus.CREATED)
  async signUp(@Body() signUpDto: SignUpDto) {
    return this.authService.signUp(signUpDto);
  }

  @Post('signin')
  @HttpCode(HttpStatus.OK)
  async signIn(@Body() signInDto: SignInDto, @Res() res: Response) {
    return this.authService.signIn(signInDto,res);
  }

  @Post('signout')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async signOut(@Request() req: any) {
    const token = req.headers.authorization?.replace('Bearer ', '');
    await this.authService.signOut(token);
    return { message: 'Successfully signed out' };
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    await this.authService.resetPassword(resetPasswordDto.email, resetPasswordDto.redirect_url);
    return { message: 'Password reset email sent successfully' };
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refresh(@Body() dto: RefreshTokenDto, @Res() res: Response) {
    return this.authService.refreshToken(dto, res);
  }

  @Get('profile')
  @UseGuards(AuthGuard)
  async getProfile(@Request() req: any) {
    const user = req.user;
    const user_profile = await this.authService.getUserProfile(user.id);
    return { user_profile };
  }
  @Get('step-count')
  @UseGuards(AuthGuard)
  async getStepCount(@Request() req: any) {
    const user = req.user;
    const step_count = await this.authService.getStepCount(user.id);
    return { step_count };
  }

  @Put('profile')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async updateProfile(@Body() updateProfileDto: UpdateProfileDto, @Request() req: any) {
    const user = req.user;
    const updatedProfile = await this.authService.updateProfile(user.id, updateProfileDto);
    return {
      status: 'success',
      code: 200,
      message: 'Profile updated successfully',
      data: updatedProfile,
      timestamp: new Date().toISOString()
    };
  }

  @Put('change-password')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async changePassword(@Body() changePasswordDto: ChangePasswordDto, @Request() req: any) {
    const user = req.user;
    await this.authService.changePassword(user.id, user.email, changePasswordDto);
    return {
      status: 'success',
      code: 200,
      message: 'Password changed successfully',
      timestamp: new Date().toISOString()
    };
  }
} 