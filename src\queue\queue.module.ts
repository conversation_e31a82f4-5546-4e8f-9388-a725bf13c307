import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { QueueService } from './queue.service';
import { QueueController } from './queue.controller';
import { 
  CampaignMessageProcessor, 
  ScheduledCampaignMessageProcessor, 
  RetryCampaignMessageProcessor 
} from './queue.processor';
import { MessageStatusService } from './message-status.service';
import { TemplateModule } from '../template/template.module';
import { MetaApiModule } from '../meta-api/meta-api.module';
import { MetaOnboardingModule } from '../meta-onboarding/meta-onboarding.module';
import { AuthModule } from '../auth/auth.module';
import { Campaign, CampaignSchema } from '../schema/campaign.schema';
import { CampaignExecution, CampaignExecutionSchema } from '../schema/campaign-execution.schema';

@Module({
  imports: [
    ConfigModule,
    TemplateModule,
    MetaApiModule,
    MetaOnboardingModule,
    AuthModule,
    MongooseModule.forFeature([
      { name: Campaign.name, schema: CampaignSchema },
      { name: CampaignExecution.name, schema: CampaignExecutionSchema }
    ]),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
          db: configService.get('REDIS_DB', 0),
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      {
        name: 'campaign-messages',
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      },
      {
        name: 'scheduled-campaign-messages',
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      },
      {
        name: 'campaign-retry-messages',
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 25,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      }
    ),
  ],
  controllers: [QueueController],
  providers: [
    QueueService,
    CampaignMessageProcessor,
    ScheduledCampaignMessageProcessor,
    RetryCampaignMessageProcessor,
    MessageStatusService,
  ],
  exports: [QueueService, BullModule],
})
export class QueueModule {}
