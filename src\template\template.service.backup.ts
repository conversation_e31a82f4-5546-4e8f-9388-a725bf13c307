import { AiService } from 'src/ai/ai.service';
import { Injectable, UnauthorizedException, BadRequestException, NotFoundException, Logger, InternalServerErrorException } from '@nestjs/common';
import { Response } from 'express';
import { SupabaseService } from 'src/supabase/supabase.service';
import { MetaApiService } from 'src/meta-api/meta-api.service';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto, CreateAiTemplateDto, CreateVoiceTemplateDto } from 'src/dto/template.dto';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { waitForDebugger } from 'inspector/promises';

@Injectable()
export class TemplateService {
      private readonly logger = new Logger(MetaApiService.name);
    constructor(
        private readonly supabaseService: SupabaseService,
        private readonly metaApiService: MetaApiService,
        private readonly configService: ConfigService,
        private readonly aiService: AiService
    ) {}

    async createTemplate(templateDto: CreateTemplateDto, req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Get user profile to get workspace_id
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (userProfile.error || !userProfile.data) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'User profile not found',
                timestamp: new Date().toISOString()
            });
        }

        // Check if template with same name and language already exists
        const { data: existingTemplate, error: checkError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('id, name, language, waba_id')
            .eq('name', templateDto.name)
            .eq('language', templateDto.language)
            .eq('waba_id', templateDto.waba_id)
            .eq('workspace_id', userProfile.data.workspace_id)
            .single();

        if (existingTemplate && !checkError) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Template with name "${templateDto.name}" and language "${templateDto.language}" already exists in your workspace. Please use a different name or language.`,
                data: {
                    existing_template: {
                        id: existingTemplate.id,
                        name: existingTemplate.name,
                        language: existingTemplate.language,
                        waba_id: existingTemplate.waba_id
                    }
                },
                timestamp: new Date().toISOString()
            });
        }

        // Get Meta credentials for the workspace
        const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('workspace_id', userProfile.data.workspace_id)
            .eq('whatsapp_business_id', templateDto.waba_id)
            .eq('status', 'Active')
            .single();

        let metaTemplateId: string | null = null;
        let metaTemplateStatus = 'DRAFT';
        let metaTemplateCategory: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION' = templateDto.category as 'MARKETING' | 'UTILITY' | 'AUTHENTICATION';

        // If Meta credentials exist, create template in Meta
        if (metaCredentials && !credentialsError) {
            try {
                // Convert our template format to Meta's format
                const metaTemplateData = this.metaApiService.convertToMetaFormat(templateDto);
                // Create template in Meta
                const metaResponse = await this.metaApiService.createTemplate(
                    metaCredentials.whatsapp_business_id,
                    metaCredentials.access_token,
                    metaTemplateData
                );

                metaTemplateId = metaResponse.id;
                metaTemplateStatus = metaResponse.status;
                metaTemplateCategory = metaResponse.category as 'MARKETING' | 'UTILITY' | 'AUTHENTICATION';

                this.logger.log(`Template created in Meta with ID: ${metaTemplateId}`);
            } catch (metaError) {
                this.logger.error(`Failed to create template in Meta: ${metaError.message}`);
                throw new BadRequestException(`Failed to create template in Meta: ${metaError.message}`);
            }
        }
     
        const payload = {
            name: templateDto.name,
            description: templateDto.description,
            type: templateDto.type,
            headerText: templateDto.headerText,
            footer: templateDto.footer,
            content: templateDto.content,
            buttons: templateDto.buttons || [],
            sections: templateDto.sections || [],
            image_url: templateDto.imageUrl,
            image_caption: templateDto.imageCaption,
            language: templateDto.language,
            variables: templateDto.variables,
            meta_template_id: metaTemplateId,
            meta_template_status: metaTemplateStatus,
            meta_template_category: metaTemplateCategory,
            workspace_id: userProfile.data.workspace_id,
            created_by: user.id,
            is_active: metaTemplateId?true:false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            waba_id: templateDto.waba_id
        };

        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .insert(payload)
            .select()
            .single();

        if (error) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Failed to create template: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }

        return res.status(201).json({
            status: 'success',
            code: 201,
            message: metaTemplateId ? 'Template created successfully in both local DB and Meta' : 'Template created successfully in local DB only',
            data: {
                ...data,
                meta_creation_success: !!metaTemplateId,
                meta_template_id: metaTemplateId,
                meta_template_status: metaTemplateStatus,
                waba_id: templateDto.waba_id,
                
            },
            timestamp: new Date().toISOString()
        });
    }
    async createDraftTemplate(templateDto: CreateTemplateDto, req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Get user profile to get workspace_id
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (userProfile.error || !userProfile.data) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'User profile not found',
                timestamp: new Date().toISOString()
            });
        }

        // Check if template with same name and language already exists
        const { data: existingTemplate, error: checkError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('id, name, language, waba_id')
            .eq('name', templateDto.name)
            .eq('language', templateDto.language)
            .eq('waba_id', templateDto.waba_id)
            .eq('workspace_id', userProfile.data.workspace_id)
            .single();

        if (existingTemplate && !checkError) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Template with name "${templateDto.name}" and language "${templateDto.language}" already exists in your workspace. Please use a different name or language.`,
                data: {
                    existing_template: {
                        id: existingTemplate.id,
                        name: existingTemplate.name,
                        language: existingTemplate.language,
                        waba_id: existingTemplate.waba_id
                    }
                },
                timestamp: new Date().toISOString()
            });
        }

        let metaTemplateId: string | null = null;
        let metaTemplateStatus = 'DRAFT';

        const payload = {
            name: templateDto.name,
            description: templateDto.description,
            type: templateDto.type,
            content: templateDto.content,
            buttons: templateDto.buttons || [],
            sections: templateDto.sections || [],
            image_url: templateDto.imageUrl,
            image_caption: templateDto.imageCaption,
            language: templateDto.language || 'en',
            variables: templateDto.variables || {},
            meta_template_id: metaTemplateId,
            meta_template_status: metaTemplateStatus,
            meta_template_category: templateDto.category,
            workspace_id: userProfile.data.workspace_id,
            created_by: user.id,
            is_active: false,
            headerText: templateDto?.headerText,
            footer: templateDto?.footer,
            waba_id: templateDto.waba_id
        };

        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .insert(payload)
            .select()
            .single();

        if (error) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Failed to save template: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }

        return res.status(201).json({
            status: 'success',
            code: 201,
            message: 'Template created successfully in local DB only',
            data: {
                ...data,
                meta_template_status: metaTemplateStatus
            },
            timestamp: new Date().toISOString()
        });
    }

    async getTemplates(req: any, query: TemplateQueryDto, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Get pagination params
        const page = query?.page && !isNaN(Number(query.page)) && Number(query.page) > 0 ? Number(query.page) : 1;
        const limit = query?.limit && !isNaN(Number(query.limit)) && Number(query.limit) > 0 ? Number(query.limit) : 10;
        const skip = (page - 1) * limit;

        // Build query
        let supabaseQuery = this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*')
            .eq('created_by', user.id);

        // Apply filters
        if (query.search) {
            supabaseQuery = supabaseQuery.or(`name.ilike.%${query.search}%,description.ilike.%${query.search}%`);
        }

        if (query.category) {
            supabaseQuery = supabaseQuery.eq('category', query.category);
        }

        if (query.type) {
            supabaseQuery = supabaseQuery.eq('type', query.type);
        }
        if (query.type) {
            supabaseQuery = supabaseQuery.eq('type', query.type);
        }
        if (query.waba_id) {
            supabaseQuery = supabaseQuery.eq('waba_id', query.waba_id);
        }

        if (query.status) {
            supabaseQuery = supabaseQuery.eq('meta_template_status', query.status);
        }

        // Apply pagination and ordering
        const { data, error } = await supabaseQuery
            .range(skip, skip + limit - 1)
            .order('created_at', { ascending: false });

        if (error) {
            throw new BadRequestException(`Failed to fetch templates: ${error.message}`);
        }

        // Get total count
        let countQuery = this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*', { count: 'exact', head: true })
            .eq('created_by', user.id);

        // Apply same filters for count
        if (query.search) {
            countQuery = countQuery.or(`name.ilike.%${query.search}%,description.ilike.%${query.search}%`);
        }

        if (query.category) {
            countQuery = countQuery.eq('category', query.category);
        }

        if (query.type) {
            countQuery = countQuery.eq('type', query.type);
        }

        if (query.language) {
            countQuery = countQuery.eq('language', query.language);
        }

        if (query.status) {
            countQuery = countQuery.eq('meta_template_status', query.status);
        }

        const { count, error: countError } = await countQuery;

        if (countError) {
            throw new BadRequestException(`Failed to get count: ${countError.message}`);
        }

        const result = {
            data,
            total: count || 0,
            page,
            limit,
            totalPages: Math.ceil((count || 0) / limit)
        };

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'Templates retrieved successfully',
            data: result,
            timestamp: new Date().toISOString()
        });
    }

    async getTemplatesByWorkspace(workspaceId: string, req: any, query: TemplateQueryDto, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Check if user has access to this workspace
        const workspaceMember = await this.supabaseService.getWorkspaceMember(parseInt(workspaceId), user.id);
        if (workspaceMember.error || !workspaceMember.data) {
            throw new UnauthorizedException('You do not have access to this workspace');
        }

        // Get pagination params
        const page = query?.page && !isNaN(Number(query.page)) && Number(query.page) > 0 ? Number(query.page) : 1;
        const limit = query?.limit && !isNaN(Number(query.limit)) && Number(query.limit) > 0 ? Number(query.limit) : 10;
        const skip = (page - 1) * limit;

        // Build query
        let supabaseQuery = this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*')
            .eq('workspace_id', parseInt(workspaceId));

        // Apply filters
        if (query.search) {
            supabaseQuery = supabaseQuery.or(`name.ilike.%${query.search}%,description.ilike.%${query.search}%`);
        }

        if (query.category) {
            supabaseQuery = supabaseQuery.eq('category', query.category);
        }

        if (query.type) {
            supabaseQuery = supabaseQuery.eq('type', query.type);
        }

        if (query.language) {
            supabaseQuery = supabaseQuery.eq('language', query.language);
        }

        if (query.status) {
            supabaseQuery = supabaseQuery.eq('meta_template_status', query.status);
        }
        if(query.waba_id){
            supabaseQuery = supabaseQuery.eq('waba_id', query.waba_id);
        }

        // Apply pagination and ordering
        const { data, error } = await supabaseQuery
            .range(skip, skip + limit - 1)
            .order('created_at', { ascending: false });

        if (error) {
            throw new BadRequestException(`Failed to fetch templates: ${error.message}`);
        }

        // Get total count
        let countQuery = this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*', { count: 'exact', head: true })
            .eq('workspace_id', parseInt(workspaceId));

        // Apply same filters for count
        if (query.search) {
            countQuery = countQuery.or(`name.ilike.%${query.search}%,description.ilike.%${query.search}%`);
        }

        if (query.category) {
            countQuery = countQuery.eq('category', query.category);
        }

        if (query.type) {
            countQuery = countQuery.eq('type', query.type);
        }

        if (query.language) {
            countQuery = countQuery.eq('language', query.language);
        }

        if (query.status) {
            countQuery = countQuery.eq('meta_template_status', query.status);
        }

        const { count, error: countError } = await countQuery;

        if (countError) {
            throw new BadRequestException(`Failed to get count: ${countError.message}`);
        }

        const result = {
            data,
            total: count,
            page,
            limit,
            totalPages: Math.ceil((count ?? 0) / limit)
        };

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'Workspace templates retrieved successfully',
            data: result,
            timestamp: new Date().toISOString()
        });
    }

    async getTemplateById(templateId: string, req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*')
            .eq('id', templateId)
            .eq('created_by', user.id)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                throw new NotFoundException('Template not found');
            }
            throw new BadRequestException(`Failed to fetch template: ${error.message}`);
        }

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'Template retrieved successfully',
            data: data,
            timestamp: new Date().toISOString()
        });
    }
    async syncAllWithMeta(wabaId: string, req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Get user's Meta credentials
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (userProfile.error || !userProfile.data) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'User profile not found',
                timestamp: new Date().toISOString()
            });
        }

        // Get Meta credentials for the workspace
        const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('workspace_id', userProfile.data.workspace_id)
            .eq('status', 'Active')
            .eq("whatsapp_business_id",wabaId)
            .single();

        if (credentialsError || !metaCredentials) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'Meta credentials not found or inactive',
                timestamp: new Date().toISOString()
            });
        }

        try {
            // Get all templates from Meta
            const metaTemplates = await this.metaApiService.getTemplates(
                metaCredentials.whatsapp_business_id,
                metaCredentials.access_token
            );

            // Get existing local templates for this waba_id
            const { data: existingLocalTemplates, error: fetchError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('meta_template_id')
                .eq('waba_id', wabaId)
                .eq('workspace_id', userProfile.data.workspace_id);

            if (fetchError) {
                throw new BadRequestException(`Failed to fetch local templates: ${fetchError.message}`);
            }

            // Create a set of existing Meta template IDs for quick lookup
            const existingMetaTemplateIds = new Set(
                existingLocalTemplates
                    .filter(t => t.meta_template_id)
                    .map(t => t.meta_template_id)
            );

            const syncResults: any[] = [];
            const errors: any[] = [];

            // Filter Meta templates that don't exist locally
            const newMetaTemplates = metaTemplates.data.filter(
                metaTemplate => !existingMetaTemplateIds.has(metaTemplate.id)
            );

            for (const metaTemplate of newMetaTemplates) {
                try {
                    // Convert Meta template to our format
                    const convertedTemplate = this.metaApiService.convertFromMetaFormat(metaTemplate);
                    
                    // Save to local database
                    const { data: savedTemplate, error: saveError } = await this.supabaseService.getClient()
                        .from('automate_whatsapp_templates')
                        .insert({
                            name: convertedTemplate.name,
                            description: convertedTemplate.description || '',
                            type: convertedTemplate.type || 'text',
                            content: convertedTemplate.content,
                            buttons: convertedTemplate.buttons || [],
                            sections: convertedTemplate.sections || [],
                            image_url: convertedTemplate.image_url,
                            image_caption: convertedTemplate.image_caption,
                            language: convertedTemplate.language,
                            variables: convertedTemplate.variables || {},
                            meta_template_id: metaTemplate.id,
                            meta_template_status: metaTemplate.status,
                            meta_template_category: metaTemplate.category,
                            workspace_id: userProfile.data.workspace_id,
                            created_by: user.id,
                            is_active: metaTemplate.status === 'APPROVED',
                            waba_id: wabaId,
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString()
                        })
                        .select()
                        .single();

                    if (saveError) {
                        throw new Error(`Failed to save template: ${saveError.message}`);
                    }

                    syncResults.push({
                        template_id: savedTemplate.id,
                        template_name: convertedTemplate.name,
                        meta_template_id: metaTemplate.id,
                        status: 'success'
                    });
                } catch (error) {
                    errors.push({
                        template_name: metaTemplate.name,
                        meta_template_id: metaTemplate.id,
                        error: error.message
                    });
                }
            }

            return res.status(200).json({
                status: 'success',
                code: 200,
                message: 'Bulk sync from Meta completed',
                data: {
                    synced: syncResults,
                    errors: errors,
                    total_meta_templates: metaTemplates.data.length,
                    new_templates_found: newMetaTemplates.length,
                    successful_syncs: syncResults.length,
                    failed_syncs: errors.length
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Failed to sync from Meta: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }
    }
    async deleteTemplate(templateId: string, waba_id: string, req: any, res: Response) {
        const user = req.user;
      
        if (!user || !user.id) {
          this.logger.error('Unauthorized access: User not found');
          throw new UnauthorizedException('User not found');
        }
      
        try {
          // Step 1: Fetch user profile
          const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
          if (userProfileError || !userProfile) {
            this.logger.error(`User profile not found for user ID ${user.id}`, userProfileError);
            throw new BadRequestException('User profile not found');
          }
      
          // Step 2: Fetch template
          const { data: existingTemplate, error: fetchError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*')
            .eq('id', templateId)
            .eq('created_by', user.id)
            .eq('waba_id', waba_id)
            .single();
      
          if (fetchError) {
            if (fetchError.code === 'PGRST116') {
              this.logger.error(`Template not found for ID ${templateId} and User ${user.id}`, fetchError);
              throw new NotFoundException('Template not found');
            }
            this.logger.error(`Failed to fetch template for ID ${templateId}`, fetchError);
            throw new BadRequestException(`Failed to fetch template: ${fetchError.message}`);
          }
      
          // Step 3: Fetch Meta credentials
          const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('created_by', user.id)
            .eq('whatsapp_business_id', waba_id)
            .eq('status', 'Active')
            .single();
      
          if (credentialsError) {
            this.logger.error(`Failed to fetch Meta credentials for User ${user.id}`, credentialsError);
            throw new BadRequestException(`Failed to fetch Meta credentials: ${credentialsError.message}`);
          }
      
          // Step 4: Delete from Meta if not DRAFT
          let deletedFromMeta = false;
          let metaErrorMessage: string | null = null;
      
          if (existingTemplate.meta_template_status !== 'DRAFT' && existingTemplate.meta_template_id) {
            try {
              await this.metaApiService.deleteTemplate(existingTemplate.name, waba_id, metaCredentials.access_token);
              deletedFromMeta = true;
              this.logger.log(`Template deleted successfully on Meta: ${existingTemplate.name}`);
            } catch (metaError) {
              metaErrorMessage = metaError?.response?.data?.error?.message || metaError.message || 'Meta API error';
              this.logger.error(`Meta API delete failed for template ${existingTemplate.name}`, metaErrorMessage);
            }
          } else {
            this.logger.log(`Skipping Meta API call for DRAFT template: ${existingTemplate.name}`);
          }
      
          // Step 5: Delete template from local DB
          const { error: deleteError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .delete()
            .eq('id', templateId)
            .eq('created_by', user.id);
      
          if (deleteError) {
            this.logger.error(`Failed to delete template from local DB: ${templateId}`, deleteError);
            throw new BadRequestException(`Failed to delete template in local DB: ${deleteError.message}`);
          }
      
          // Step 6: Handle Meta error
          if (metaErrorMessage) {
            // Template deleted locally but failed in Meta, inform user
            return res.status(400).json({
              status: 'error',
              code: 400,
              message: `Template deleted locally but failed in Meta: ${metaErrorMessage}`,
              templateId,
              template_name: existingTemplate.name,
              meta_template_status: existingTemplate.meta_template_status,
              deleted_from_meta: false,
              timestamp: new Date().toISOString()
            });
          }
      
          // Step 7: Return success response
          const message =
            existingTemplate.meta_template_status === 'DRAFT'
              ? 'Draft template deleted successfully from local database'
              : 'Template deleted successfully from both Meta and local database';
      
          return res.status(200).json({
            status: 'success',
            code: 200,
            message,
            data: {
              templateId,
              template_name: existingTemplate.name,
              meta_template_status: existingTemplate.meta_template_status,
              deleted_from_meta: deletedFromMeta
            },
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.error('Unexpected error in deleting template', error.message || error);
          // Re-throw known HTTP exceptions
          if (error instanceof BadRequestException || error instanceof NotFoundException || error instanceof UnauthorizedException) {
            throw error;
          }
          // Otherwise, internal server error
          throw new InternalServerErrorException('Something went wrong while deleting the template');
        }
      }
      
      

    // ------------------------------------------------------- abave verified and tested---------------------------

    //   working on that ---------------------------------
   
    




    async updateTemplate(templateId: string, updateTemplateDto: UpdateTemplateDto, req: any, res: Response) {
        console.log("templateId", templateId);
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Check if template exists and belongs to user
        const { data: existingTemplate, error: fetchError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*')
            .eq('id', templateId)
            .eq('created_by', user.id)
            .single();
            
        if (fetchError) {
            if (fetchError.code === 'PGRST116') {
                throw new NotFoundException('Template not found');
            }
            throw new BadRequestException(`Failed to fetch template: ${fetchError.message}`);
        }

        let metaUpdateSuccess = false;
        let metaResponse: any = null;

        // Get user profile first (outside try-catch so errors propagate)
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (!userProfile.data) {
            throw new BadRequestException('User profile not found');
        }

        try {
            // Get Meta credentials

            const { data: metaCredentials } = await this.supabaseService.getClient()
                .from('automate_whatsapp_meta_credentials')
                .select('*')
                .eq('created_by', user.id)
                .eq('status', 'Active')
                .eq('whatsapp_business_id', existingTemplate.waba_id)
                .single();

            if (metaCredentials) {
                // Prepare updated template data
                const updatedTemplateData = {
                    ...existingTemplate,
                    ...updateTemplateDto
                };
                const metaTemplateData = this.metaApiService.convertToMetaFormat(updatedTemplateData);

                if (existingTemplate.meta_template_id) {
                    // Update existing template in Meta
                    console.log(`Updating existing Meta template: ${existingTemplate.meta_template_id}`);
                    await this.metaApiService.updateTemplate(
                        existingTemplate.meta_template_id,
                        metaCredentials.access_token,
                        metaTemplateData
                    );
                    metaUpdateSuccess = true;
                    console.log(`Template updated in Meta: ${existingTemplate.meta_template_id}`);
                } else {
                    // Create new template in Meta
                    console.log("No meta template id, creating new template in Meta");
                    metaResponse = await this.metaApiService.createTemplate(
                        existingTemplate.waba_id,
                        metaCredentials.access_token,
                        metaTemplateData
                    );
                    console.log("metaResponse", metaResponse);
                    metaUpdateSuccess = true;
                }
            }
        } catch (metaError) {
            console.error('Failed to update/create template in Meta:', metaError.message);
            // Continue with local update even if Meta update fails
        }

        // Update template in local database
        const { category, ...updateData } = updateTemplateDto;
        const updatePayload: any = {
            ...updateData,
            updated_at: new Date().toISOString()
        };

        // Add Meta-specific fields if we created a new template in Meta
        if (metaResponse) {
            updatePayload.meta_template_id = metaResponse.id;
            updatePayload.meta_template_status = metaResponse.status;
            updatePayload.meta_template_category = this.metaApiService.convertToMetaFormat({
                ...existingTemplate,
                ...updateTemplateDto
            }).category;
        }

        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .update(updatePayload)
            .eq('id', templateId)
            .eq('created_by', user.id)
            .select()
            .single();

        if (error) {
            throw new BadRequestException(`Failed to update template: ${error.message}`);
        }

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: metaUpdateSuccess ? 'Template updated successfully in both local DB and Meta' : 'Template updated successfully in local DB only',
            data: {
                ...data,
                meta_update_success: metaUpdateSuccess
            },
            timestamp: new Date().toISOString()
        });
    }

   

    async syncWithMeta(templateId: string, req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Check if template exists and belongs to user
        const { data: template, error: fetchError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('*')
            .eq('id', templateId)
            .eq('created_by', user.id)
            .single();

        if (fetchError) {
            if (fetchError.code === 'PGRST116') {
                throw new NotFoundException('Template not found');
            }
            throw new BadRequestException(`Failed to fetch template: ${fetchError.message}`);
        }

        // Get user's Meta credentials
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (userProfile.error || !userProfile.data) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'User profile not found',
                timestamp: new Date().toISOString()
            });
        }

        // Get Meta credentials for the workspace
        const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('created_by', user.id)
            .eq('status', 'Active')
            .single();

        if (credentialsError || !metaCredentials) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'Meta credentials not found or inactive',
                timestamp: new Date().toISOString()
            });
        }

        try {
            // Convert template to Meta format
            const metaTemplateData = this.metaApiService.convertToMetaFormat(template);

            // Create template in Meta
            const metaResponse = await this.metaApiService.createTemplate(
                metaCredentials.whatsapp_business_id,
                metaCredentials.access_token,
                metaTemplateData
            );

            // Update local template with Meta template ID
            const { data: updatedTemplate, error: updateError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .update({
                    meta_template_id: metaResponse.id,
                    meta_template_status: metaResponse.status,
                    meta_template_category: template.meta_template_category,
                    updated_at: new Date().toISOString()
                })
                .eq('id', templateId)
                .select()
                .single();

            if (updateError) {
                throw new BadRequestException(`Failed to update template: ${updateError.message}`);
            }

            return res.status(200).json({
                status: 'success',
                code: 200,
                message: 'Template synced with Meta successfully',
                data: {
                    local_template: updatedTemplate,
                    meta_template: metaResponse
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Failed to sync with Meta: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }
    }

    async getMetaTemplates(req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Get user's Meta credentials
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (userProfile.error || !userProfile.data) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'User profile not found',
                timestamp: new Date().toISOString()
            });
        }

        // Get Meta credentials for the workspace
        const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('workspace_id', userProfile.data.workspace_id)
            .eq('status', 'Active')
            .single();

        if (credentialsError || !metaCredentials) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'Meta credentials not found or inactive',
                timestamp: new Date().toISOString()
            });
        }

        try {
            const metaTemplates = await this.metaApiService.getTemplates(
                metaCredentials.whatsapp_business_id,
                metaCredentials.access_token
            );

            // Convert Meta templates to our format
            const convertedTemplates = metaTemplates.data.map(template => 
                this.metaApiService.convertFromMetaFormat(template)
            );
            return res.status(200).json({
                status: 'success',
                code: 200,
                message: 'Meta templates retrieved successfully',
                data: {
                    templates: convertedTemplates,
                    total: convertedTemplates.length,
                    paging: metaTemplates.paging
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Failed to get Meta templates: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }
    }
 
    async generateAiTemplate(aiTemplateDto: CreateAiTemplateDto, req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Get user profile to get workspace_id
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (userProfile.error || !userProfile.data) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'User profile not found',
                timestamp: new Date().toISOString()
            });
        }

        try {
            // Generate template using AI
            const generatedTemplate = await this.aiService.generateTemplate(aiTemplateDto);
            
            // Save as draft template (not submitted to Meta)
            const payload = {
                name: generatedTemplate.name,
                description: generatedTemplate.description,
                type: generatedTemplate.type,
                headerText: generatedTemplate.headerText,
                footer: generatedTemplate.footer,
                content: generatedTemplate.content,
                buttons: generatedTemplate.buttons || [],
                sections: generatedTemplate.sections || [],
                image_url: generatedTemplate.imageUrl,
                image_caption: generatedTemplate.imageCaption,
                language: generatedTemplate.language || 'en',
                variables: generatedTemplate.variables || {},
                meta_template_id: null, // Not submitted to Meta yet
                meta_template_status: 'DRAFT',
                meta_template_category: generatedTemplate.meta_template_category,
                workspace_id: userProfile.data.workspace_id,
                created_by: user.id,
                is_active: false, // Draft templates are not active
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                ai_generated: true,
                waba_id: aiTemplateDto.waba_id
            };

            const { data, error } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .insert(payload)
                .select()
                .single();

            if (error) {
                return res.status(400).json({
                    status: 'error',
                    code: 400,
                    message: `Failed to save AI-generated template: ${error.message}`,
                    timestamp: new Date().toISOString()
                });
            }

            return res.status(201).json({
                status: 'success',
                code: 201,
                message: 'AI template generated and saved as draft successfully',
                data: {
                    data
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            this.logger.error('Failed to generate AI template:', error);
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Failed to generate AI template: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }
    }
    async generateTemplateFromVoice(file: any, body: any, req: any, res: Response): Promise<any> {
        try {
            this.logger.log('Processing voice input for template generation...');
            
            if (!file) {
                return res.status(400).json({
                    status: 'error',
                    code: 400,
                    message: 'Audio file is required',
                    timestamp: new Date().toISOString()
                });
            }

            // Extract WABA ID from form data
            const wabaId = body?.waba_id;
            console.log("wabaid",wabaId)
            if (!wabaId) {
                return res.status(400).json({
                    status: 'error',
                    code: 400,
                    message: 'WABA ID is required',
                    timestamp: new Date().toISOString()
                });
            }
            
            this.logger.log(`Extracted WABA ID from form data: ${wabaId}`);
            
            // Step 1: Transcribe audio to text
            const transcribedText = await this.aiService.transcribeAudio(file, file.mimetype);
            
            if (!transcribedText || transcribedText.trim() === '') {
                throw new BadRequestException('No text found in audio transcription');
            }
      
            this.logger.log(`Transcribed text: ${transcribedText}`);
            
            // Step 2: Generate template using the transcribed text and extracted WABA ID
            return await this.generateAiTemplate({prompt: transcribedText, waba_id: wabaId}, req, res);
            
        } catch (error) {
            this.logger.error('Failed to generate template from voice:', error);
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Voice-based template generation failed: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }
    }
    
  

} 