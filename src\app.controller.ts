import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { DatabaseService } from './database/database.service';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly databaseService: DatabaseService,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  async getHealth() {
    const dbConnected = await this.databaseService.isDatabaseConnected();
    const dbStatus = this.databaseService.getConnectionStatus();
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: {
        connected: dbConnected,
        status: dbStatus,
      },
    };
  }

  @Get('health/database')
  async getDatabaseHealth() {
    const dbConnected = await this.databaseService.isDatabaseConnected();
    const dbStatus = this.databaseService.getConnectionStatus();
    const dbInfo = await this.databaseService.getDatabaseInfo();
    
    return {
      connected: dbConnected,
      status: dbStatus,
      info: dbInfo,
    };
  }
}
