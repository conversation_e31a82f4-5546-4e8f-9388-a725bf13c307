import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CustomField } from '../schema/custom-field.schema';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { SupabaseService } from '../supabase/supabase.service';

@Injectable()
export class CustomFieldsService {
    private readonly logger = new Logger(CustomFieldsService.name);

    constructor(
        @InjectModel(CustomField.name) private readonly customFieldModel: Model<CustomField>,
        private readonly supabaseService: SupabaseService,
    ) {}

    private async getWorkspaceIdOrThrow(userId: string): Promise<number> {
        const { data, error } = await this.supabaseService.getUserProfile(userId);
        if (error || !data?.workspace_id) {
            throw new BadRequestException('User workspace not found');
        }
        return data.workspace_id as number;
    }

    async create(userId: string, dto: CreateCustomFieldDto) {
        const workspaceId = await this.getWorkspaceIdOrThrow(userId);
        try {
            const created = await this.customFieldModel.create({
                ...dto,
                workspaceId,
                createdBy: userId,
            });
            return created;
        } catch (error: any) {
            if (error?.code === 11000) {
                throw new BadRequestException('A custom field with this key already exists in this workspace');
            }
            this.logger.error('Failed to create custom field', error);
            throw new BadRequestException('Failed to create custom field');
        }
    }

    async findAll(userId: string) {
        const workspaceId = await this.getWorkspaceIdOrThrow(userId);
        return this.customFieldModel.find({ workspaceId }).sort({ createdAt: -1 }).lean();
    }

    async findOne(userId: string, id: string) {
        const workspaceId = await this.getWorkspaceIdOrThrow(userId);
        const doc = await this.customFieldModel.findOne({ _id: id, workspaceId }).lean();
        if (!doc) throw new NotFoundException('Custom field not found');
        return doc;
    }

    async update(userId: string, id: string, dto: UpdateCustomFieldDto) {
        const workspaceId = await this.getWorkspaceIdOrThrow(userId);
        try {
            const updated = await this.customFieldModel.findOneAndUpdate(
                { _id: id, workspaceId },
                { $set: dto },
                { new: true },
            );
            if (!updated) throw new NotFoundException('Custom field not found');
            return updated;
        } catch (error: any) {
            if (error?.code === 11000) {
                throw new BadRequestException('A custom field with this key already exists in this workspace');
            }
            this.logger.error('Failed to update custom field', error);
            throw new BadRequestException('Failed to update custom field');
        }
    }

    async remove(userId: string, id: string) {
        const workspaceId = await this.getWorkspaceIdOrThrow(userId);
        const deleted = await this.customFieldModel.findOneAndDelete({ _id: id, workspaceId });
        if (!deleted) throw new NotFoundException('Custom field not found');
        return { message: 'Custom field deleted successfully' };
    }
}


