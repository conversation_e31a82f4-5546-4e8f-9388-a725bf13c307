import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Campaign, CampaignDocument } from '../schema/campaign.schema';
import { Contact, ContactDocument } from '../schema/contacts.schema';
import { KafkaService, CampaignMessage, CampaignBatchMessage } from '../kafka/kafka.service';
import { TemplateService } from '../template/template.service';

@Injectable()
export class CampaignProcessorService {
  private readonly logger = new Logger(CampaignProcessorService.name);

  constructor(
    @InjectModel(Campaign.name) private campaignModel: Model<CampaignDocument>,
    @InjectModel(Contact.name) private contactModel: Model<ContactDocument>,
    private readonly kafkaService: KafkaService,
    private readonly templateService: TemplateService,
  ) {}

  async startCampaign(campaignId: string, userId: string): Promise<void> {
    try {
      const campaign = await this.campaignModel.findById(campaignId);
      if (!campaign) {
        throw new Error('Campaign not found');
      }
      if (campaign.status !== 'DRAFT' && campaign.status !== 'SCHEDULED') {
        throw new Error('Campaign can only be started from DRAFT or SCHEDULED status');
      }

      // Update campaign status to ACTIVE
      await this.campaignModel.findByIdAndUpdate(campaignId, {
        status: 'ACTIVE',
        started_at: new Date(),
        is_active: true,
      });
      this.logger.log(`Starting campaign ${campaignId} with ${campaign.total_contacts} contacts`);

      // Process campaign based on send_type
      switch (campaign.send_type) {
        case 'IMMEDIATE':
          await this.processImmediateCampaign(campaign, userId);
          break;
        case 'SCHEDULED':
          await this.processScheduledCampaign(campaign, userId);
          break;
        case 'RECURRING':
          await this.processRecurringCampaign(campaign, userId);
          break;
        default:
          throw new Error(`Unsupported send type: ${campaign.send_type}`);
      }

    } catch (error) {
      this.logger.error(`Failed to start campaign ${campaignId}: ${error.message}`, error.stack);
      
      // Update campaign status to FAILED
      await this.campaignModel.findByIdAndUpdate(campaignId, {
        status: 'FAILED',
        is_active: false,
      });

      throw error;
    }
  }

  private async processImmediateCampaign(campaign: any, userId: string): Promise<void> {
    try {
      // Get all contacts for the campaign
      const contacts = await this.getCampaignContacts(campaign);
      console.log("contacts", contacts);
      if (contacts.length === 0) {
        this.logger.warn(`No contacts found for campaign ${campaign._id}`);
        await this.completeCampaign(campaign._id.toString());
        return;
      }

      // Process contacts in batches
      const batchSize = campaign.batch_size || 100;
      const batches = this.chunkArray(contacts, batchSize);
      this.logger.log(`Processing ${contacts.length} contacts in ${batches.length} batches for campaign ${campaign._id}`);
      console.log("batches", batches);

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        await this.processBatch(campaign, batch, userId, i);
        
        // Add delay between batches to respect rate limits
        if (i < batches.length - 1) {
          await this.delay(1000); // 1 second delay between batches
        }
      }

      // Mark campaign as completed
      await this.completeCampaign(campaign._id.toString());

    } catch (error) {
      this.logger.error(`Failed to process immediate campaign ${campaign._id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async processScheduledCampaign(campaign: any, userId: string): Promise<void> {
    try {
      if (!campaign.scheduled_at) {
        throw new Error('Scheduled campaign must have a scheduled_at date');
      }

      // Check if it's time to send
      if (new Date() < campaign.scheduled_at) {
        this.logger.log(`Campaign ${campaign._id} is scheduled for ${campaign.scheduled_at}, waiting...`);
        return;
      }

      // Process as immediate campaign
      await this.processImmediateCampaign(campaign, userId);

    } catch (error) {
      this.logger.error(`Failed to process scheduled campaign ${campaign._id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async processRecurringCampaign(campaign: any, userId: string): Promise<void> {
    try {
      if (!campaign.recurring_settings) {
        throw new Error('Recurring campaign must have recurring settings');
      }

      // Check if it's time to send based on recurring settings
      if (this.shouldSendRecurringCampaign(campaign)) {
        await this.processImmediateCampaign(campaign, userId);
        
        // Update next scheduled time
        await this.updateNextRecurringSchedule(campaign);
      }

    } catch (error) {
      this.logger.error(`Failed to process recurring campaign ${campaign._id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async processBatch(
    campaign: any,
    contacts: any[],
    userId: string,
    batchIndex: number,
  ): Promise<void> {
    console.log("contacts", contacts);
    try {
      const messages: CampaignMessage[] = contacts.map((contact) => ({
        campaignId: campaign._id.toString(),
        contactId:contact._id ? contact._id.toString() : null,
        phoneNumber: contact.phoneNumber,
        countryCode: contact.countryCode,
        templateId: campaign.template_id,
        phoneNumberId: campaign.phone_number_id,
        variableMapping: this.buildVariableMapping(campaign, contact),
        workspaceId: campaign.workspace_id,
        userId: userId,
        retryCount: 0,
        priority: this.getContactPriority(contact),
      }));

      const batchMessage: CampaignBatchMessage = {
        campaignId: campaign._id.toString(),
        messages,
        batchSize: messages.length,
        totalContacts: campaign.total_contacts,
        workspaceId: campaign.workspace_id,
        userId: userId,
      };

      // Send batch to Kafka
      await this.kafkaService.sendCampaignBatch(batchMessage);

      this.logger.log(`Sent batch ${batchIndex + 1} with ${messages.length} messages for campaign ${campaign._id}`);

    } catch (error) {
      this.logger.error(`Failed to process batch ${batchIndex} for campaign ${campaign._id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async getCampaignContacts(campaign: any): Promise<any[]> {
    const contacts: any[] = [];

    // Get contacts from message logs (already filtered during campaign creation)
    if (campaign.message_logs && campaign.message_logs.length > 0) {
      // Separate CSV contacts and database contacts
      const csvContacts: any[] = [];
      const dbContactIds: any[] = [];

      for (const log of campaign.message_logs) {
        if (log.status === 'PENDING') {
          if (log.contact_id) {
            dbContactIds.push(log.contact_id);
          } else {
            const csvContact = this.recreateCsvContact(campaign, log.phone);
            if (csvContact) {
              csvContacts.push(csvContact);
            }
          }
        }
      }

      // Get database contacts
      if (dbContactIds.length > 0) {
        const dbContacts = await this.contactModel.find({
          _id: { $in: dbContactIds },
          subscribed: { $ne: false },
        }).lean();
        contacts.push(...dbContacts);
      }

      // Add CSV contacts
      contacts.push(...csvContacts);
    }

    return contacts;
  }

  private recreateCsvContact(campaign: any, phone: string): any | null {
    if (!campaign.csv_contacts_string) {
      return null;
    }

    try {
      const lines = campaign.csv_contacts_string.trim().split('\n').filter(line => line.trim());
      const dataLines = lines.slice(1); // Skip header

      // Use mapping if provided, otherwise use default (first column as phone)
      const phoneColumn = campaign.csv_mapping?.phone_column ?? 0;
      const nameColumn = campaign.csv_mapping?.name_column;
      const emailColumn = campaign.csv_mapping?.email_column;
      const countryCodeColumn = campaign.csv_mapping?.country_code_column;
      const customFieldMapping = campaign.csv_mapping?.custom_field_mapping ?? {};

      for (const line of dataLines) {
        const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
        
        if (columns[phoneColumn] === phone) {
          const contact = {
            phoneNumber: phone,
            firstName: nameColumn !== undefined && columns[nameColumn] ? columns[nameColumn] : '',
            lastName: '',
            countryCode: countryCodeColumn !== undefined && columns[countryCodeColumn] ? columns[countryCodeColumn] : '',
            email: emailColumn !== undefined && columns[emailColumn] ? columns[emailColumn] : '',
            customFields: {},
            subscribed: true,
          };

          // Add custom fields based on mapping
          Object.keys(customFieldMapping).forEach(fieldName => {
            const columnIndex = customFieldMapping[fieldName];
            if (columns[columnIndex]) {
              contact.customFields[fieldName] = columns[columnIndex];
            }
          });

          return contact;
        }
      }
    } catch (error) {
      this.logger.error('Error recreating CSV contact:', error);
    }

    return null;
  }

  private buildVariableMapping(campaign: any, contact: any): Record<string, any> {
    const mapping: Record<string, any> = {};
    console.log("campaign.variable_mapping", campaign.variable_mapping);
    if (campaign.variable_mapping && typeof campaign.variable_mapping === 'object') {
      // Replace template variables with contact data
      Object.keys(campaign.variable_mapping).forEach(section => {
        mapping[section] = {};
        const sectionData = campaign.variable_mapping[section];
        
        if (sectionData && typeof sectionData === 'object') {
          Object.keys(sectionData).forEach(variable => {
            const value = sectionData[variable];
            mapping[section][variable] = this.replaceTemplateVariables(value, contact);
          });
        }
      });
    } else {
      // Fallback: provide basic contact variables if no mapping is defined
      mapping.body = {
        firstName: contact.firstName || '',
        lastName: contact.lastName || '',
        email: contact.email || '',
        phoneNumber: contact.phoneNumber || '',
      };
    }

    return mapping;
  }

  private replaceTemplateVariables(template: string, contact: any): string {
    if (!template || typeof template !== 'string') return template || '';

    // Replace common variables
    let result = template
      .replace(/\{\{firstName\}\}/g, contact.firstName || '')
      .replace(/\{\{lastName\}\}/g, contact.lastName || '')
      .replace(/\{\{email\}\}/g, contact.email || '')
      .replace(/\{\{phoneNumber\}\}/g, contact.phoneNumber || '');

    // Replace numbered variables {{1}}, {{2}}, etc.
    result = result.replace(/\{\{(\d+)\}\}/g, (match, number) => {
      const index = parseInt(number) - 1;
      const customFields = contact.customFields || {};
      const customFieldKeys = Object.keys(customFields);
      
      if (index < customFieldKeys.length) {
        return customFields[customFieldKeys[index]] || '';
      }
      
      return match; // Keep original if no replacement found
    });

    return result;
  }

  private getContactPriority(contact: any): 'HIGH' | 'NORMAL' | 'LOW' {
    // Implement priority logic based on contact properties
    // For example, VIP customers, recent activity, etc.
    if (contact.tagsId && contact.tagsId.includes('vip')) {
      return 'HIGH';
    }
    
    if (contact.lastActivity && new Date(contact.lastActivity) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) {
      return 'HIGH';
    }

    return 'NORMAL';
  }

  private shouldSendRecurringCampaign(campaign: any): boolean {
    if (!campaign.recurring_settings) return false;

    const { frequency, interval, end_date, max_occurrences } = campaign.recurring_settings;
    const now = new Date();

    // Check end date
    if (end_date && now > new Date(end_date)) {
      return false;
    }

    // Check max occurrences
    if (max_occurrences && campaign.sent_count >= max_occurrences) {
      return false;
    }

    // Check frequency
    const lastSent = campaign.started_at || campaign.createdAt || new Date();
    if (!lastSent) return true;

    const timeSinceLastSent = now.getTime() - lastSent.getTime();
    const intervalMs = this.getIntervalInMs(frequency, interval);

    return timeSinceLastSent >= intervalMs;
  }

  private getIntervalInMs(frequency: string, interval: number): number {
    switch (frequency) {
      case 'DAILY':
        return interval * 24 * 60 * 60 * 1000;
      case 'WEEKLY':
        return interval * 7 * 24 * 60 * 60 * 1000;
      case 'MONTHLY':
        return interval * 30 * 24 * 60 * 60 * 1000;
      default:
        return 24 * 60 * 60 * 1000; // Default to daily
    }
  }

  private async updateNextRecurringSchedule(campaign: any): Promise<void> {
    if (!campaign.recurring_settings) return;

    const { frequency, interval } = campaign.recurring_settings;
    const nextScheduled = new Date();
    
    switch (frequency) {
      case 'DAILY':
        nextScheduled.setDate(nextScheduled.getDate() + interval);
        break;
      case 'WEEKLY':
        nextScheduled.setDate(nextScheduled.getDate() + (interval * 7));
        break;
      case 'MONTHLY':
        nextScheduled.setMonth(nextScheduled.getMonth() + interval);
        break;
    }

    await this.campaignModel.findByIdAndUpdate(campaign._id, {
      scheduled_at: nextScheduled,
      status: 'SCHEDULED',
    });
  }

  private async completeCampaign(campaignId: string): Promise<void> {
    await this.campaignModel.findByIdAndUpdate(campaignId, {
      status: 'COMPLETED',
      completed_at: new Date(),
      is_active: false,
    });

    this.logger.log(`Campaign ${campaignId} completed successfully`);
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async updateMessageStatus(
    campaignId: string,
    contactId: string,
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED',
    messageId?: string,
    errorMessage?: string,
  ): Promise<void> {
    try {
      const updateData: any = {
        $inc: {
          [`delivery_stats.${status.toLowerCase()}`]: 1,
          pending_count: -1,
        },
      };

      // Update message log
      updateData.$set = {
        [`message_logs.$[elem].status`]: status,
        [`message_logs.$[elem].${status.toLowerCase()}_at`]: new Date(),
      };

      if (messageId) {
        updateData.$set[`message_logs.$[elem].message_id`] = messageId;
      }

      if (errorMessage) {
        updateData.$set[`message_logs.$[elem].error_message`] = errorMessage;
      }

             // Handle both ObjectId and phone number for CSV contacts
       let contactIdFilter: any;
       let arrayFilter: any;

       if (contactId.startsWith('csv_')) {
         // CSV contact - use phone number for filtering
         const phone = contactId.replace('csv_', '');
         contactIdFilter = { _id: campaignId, 'message_logs.phone': phone };
         arrayFilter = [{ 'elem.phone': phone }];
       } else {
         // Database contact (ObjectId)
         contactIdFilter = { _id: campaignId, 'message_logs.contact_id': new Types.ObjectId(contactId) };
         arrayFilter = [{ 'elem.contact_id': new Types.ObjectId(contactId) }];
       }

      await this.campaignModel.updateOne(
        contactIdFilter,
        updateData,
        {
          arrayFilters: arrayFilter,
        },
      );

      this.logger.debug(`Updated message status for campaign ${campaignId}, contact ${contactId}: ${status}`);
    } catch (error) {
      this.logger.error(`Failed to update message status: ${error.message}`, error.stack);
    }
  }
}
