import { BadRequestException, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Tag } from 'src/schema/tag.schema';
import { SupabaseService } from 'src/supabase/supabase.service';

@Injectable()
export class TagsService {
  private readonly logger = new Logger(TagsService.name);
  constructor(
    @InjectModel(Tag.name) private tagModel: Model<Tag>,
    private readonly supabaseService: SupabaseService,
  ) {}

  async createTag(body: { name: string; background_color?: string,text_color?: string }, req: any, res: any) {
    const user = req.user;
    if (!user?.id) throw new UnauthorizedException('User not found');
    if (!body?.name) throw new BadRequestException('name is required');

    const { data: userProfile, error } = await this.supabaseService.getUserProfile(user.id);
    if (error || !userProfile?.workspace_id) {
      this.logger.error(`User profile/workspace missing: ${error}`);
      throw new BadRequestException('User not found');
    }
    const workspaceId = userProfile.workspace_id;

    try {
      const tag = await this.tagModel.create({
        name: body.name.trim(),
        text_color: body.text_color,
        background_color: body.background_color,
        createdBy: user.id,
        workspaceId,
      });
      return res.status(201).json({ status: 'success', code: 201, data: tag });
    } catch (e: any) {
      if (e?.code === 11000) {
        return res.status(409).json({ status: 'error', code: 409, message: 'Tag name already exists in workspace' });
      }
      this.logger.error('Failed to create tag', e);
      throw new BadRequestException('Failed to create tag');
    }
  }

  async listTags(req: any, res: any) {
    const user = req.user;
    if (!user?.id) throw new UnauthorizedException('User not found');
    const { data: userProfile, error } = await this.supabaseService.getUserProfile(user.id);
    if (error || !userProfile?.workspace_id) throw new BadRequestException('User not found');
    const workspaceId = userProfile.workspace_id;

    const tags = await this.tagModel.find({ workspaceId }).sort({ name: 1 }).lean();
    return res.status(200).json({ status: 'success', code: 200, data: tags });
  }
}


