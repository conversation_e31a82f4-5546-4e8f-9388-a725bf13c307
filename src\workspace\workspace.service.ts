import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { CreateMemberdto } from 'src/dto/workspaceMemeber.dto';

@Injectable()
export class WorkspaceService {
  private readonly logger = new Logger(WorkspaceService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
  ) {}

  async create(createWorkspaceDto: CreateWorkspaceDto, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;

      // Check if user exists in user_profile table
      const {data:userProfile,error:userProfileError} = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user already has a workspace (due to unique constraint)
      const existingWorkspace = await this.supabaseService.getWorkspaceByCreatedBy(user.id);
      console.log("existingWorkspace", existingWorkspace);
      if (existingWorkspace) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User already has a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Prepare workspace data with user ID from request
      const workspaceData = {
        ...createWorkspaceDto,
        created_by: user.id,
        status: 'active',
        trial_expiry:   new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        waba_access: true,
      };
      const result = await this.supabaseService.insertWorkspace(workspaceData);
      console.log("result", result)
      if (result.error) {
        this.logger.error('Failed to create workspace:', result.error);
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Failed to create workspace',
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        });
      }

      // Update user profile with workspace ID
      const updateUserProfile = await this.supabaseService.updateUserProfile(user.id, {
        workspace_id: result.data.id,
        updated_at: new Date().toISOString()
      });

      if (updateUserProfile.error) {
        this.logger.error('Failed to update user profile:', updateUserProfile.error);
        
        // Rollback: Delete the workspace since user profile update failed
        const deleteWorkspace = await this.supabaseService.deleteWorkspace(result.data.id);
        
        if (deleteWorkspace.error) {
          this.logger.error('Failed to rollback workspace deletion:', deleteWorkspace.error);
          // Critical: Both workspace creation and rollback failed
          return res.status(500).json({
            status: 'error',
            code: 500,
            message: 'Critical error: Workspace created but profile update failed and rollback also failed. Please contact support immediately.',
            data: {
              workspace_id: result.data.id,
              user_id: user.id,
              error_details: 'Both workspace creation and rollback failed'
            },
            timestamp: new Date().toISOString(),
            request_id: req.headers['x-request-id'] || 'unknown'
          });
        }
        
        // Rollback successful
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to create workspace due to user profile update error. Please try again.',
          data: {
            user_id: user.id,
            error_details: 'User profile update failed, workspace rolled back'
          },
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        });
      }

      // Add user as workspace member (owner/admin role)
      const insertWorkspaceMember = await this.supabaseService.insertWorkspaceMember({
        workspace_id: result.data.id,
        user_id: user.id,
        role: 'Admin', // or 'admin' based on your requirements
        status: 'active',
        waba_access: true
      });

      if (insertWorkspaceMember.error) {
        this.logger.error('Failed to add user as workspace member:', insertWorkspaceMember.error);
        
        // Rollback: Delete workspace and revert user profile
        const deleteWorkspace = await this.supabaseService.deleteWorkspace(result.data.id);
        const revertUserProfile = await this.supabaseService.updateUserProfile(user.id, {
          workspace_id: null,
          updated_at: new Date().toISOString()
        });
        
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to create workspace due to member creation error. Please try again.',
          data: {
            user_id: user.id,
            error_details: 'Workspace member creation failed, workspace rolled back'
          },
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        });
      }

      // Create admin role with permissions for this workspace
      const createAdminRole = await this.supabaseService.createAdminRoleWithPermissions(result.data.id);

      if (createAdminRole.error) {
        this.logger.error('Failed to create admin role:', createAdminRole.error);
        
        // Rollback: Delete workspace member, workspace, and revert user profile
        const deleteWorkspace = await this.supabaseService.deleteWorkspace(result.data.id);
        const revertUserProfile = await this.supabaseService.updateUserProfile(user.id, {
          workspace_id: null,
          updated_at: new Date().toISOString()
        });
        
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to create workspace due to role creation error. Please try again.',
          data: {
            user_id: user.id,
            error_details: 'Admin role creation failed, workspace rolled back'
          },
          timestamp: new Date().toISOString()
        });
      }

      // Insert automate whatsapp member with the created role
      const insertAutomateMember = await this.supabaseService.insertAutomateWhatsappMember({
        workspace_member_id: insertWorkspaceMember.data.id,
        role_id: Array.isArray(createAdminRole.data) ? createAdminRole.data[0]?.role_id : createAdminRole.data?.role_id, // From the function response
        user_id: user.id,
        workspace_id: result.data.id,
        status: 'active'
      });

      if (insertAutomateMember.error) {
        this.logger.error('Failed to add automate whatsapp member:', insertAutomateMember.error);
        
        // Rollback: Delete workspace member, workspace, and revert user profile
        const deleteWorkspace = await this.supabaseService.deleteWorkspace(result.data.id);
        const revertUserProfile = await this.supabaseService.updateUserProfile(user.id, {
          workspace_id: null,
          updated_at: new Date().toISOString()
        });
        
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to create workspace due to automate member creation error. Please try again.',
          data: {
            user_id: user.id,
            error_details: 'Automate whatsapp member creation failed, workspace rolled back'
          },
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        });
      }

      return res.status(201).json({
        status: 'success',
        code: 201,
        message: 'Workspace created successfully with user as admin',
        data: {
          workspace: result.data,
          created_at: new Date().toISOString(),
          user_id: user.id,
          user_profile_updated: true,
          user_profile: updateUserProfile.data,
          workspace_member: insertWorkspaceMember.data,
          admin_role: createAdminRole.data,
          automate_member: insertAutomateMember.data
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.error('Create workspace failed:', error);
      
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] || 'unknown'
      });
    }
  }
  async addMemberToWorkspace(memberData: CreateMemberdto, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;
      
      // Validate that the requesting user has a profile and get their workspace
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      console.log("userProfile", userProfile);
      if (!userProfile || userProfileError) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'User profile not found',
          timestamp: new Date().toISOString(),
        });
      }

      // Get the workspace_id from the requesting user's profile
      const workspaceId = userProfile.workspace_id;
      if (!workspaceId) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'You are not associated with any workspace',
          timestamp: new Date().toISOString(),
        });
      }



      // Validate that the workspace exists
      const { data: workspace, error: workspaceError } = await this.supabaseService.getWorkspaceById(workspaceId);
      if (!workspace || workspaceError) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Workspace not found',
          timestamp: new Date().toISOString(),
        });
      }

            // Check if email already exists
      const existingUserByEmail = await this.supabaseService.getUserProfileByEmail(memberData.email);
      if (existingUserByEmail) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User with this email already exists',
          timestamp: new Date().toISOString(),
        });
      }

      // Create new user in Supabase Auth
      const signUpResult = await this.supabaseService.signUp(
        memberData.email,
        memberData.password,
        {
          first_name: memberData.first_name,
          last_name: memberData.last_name,
          phoneNumber: memberData.phone,
          countrycode: memberData.country_code,
          country: memberData.country,
        }
      );

      if (signUpResult.error) {
        this.logger.error('Failed to create user in auth:', signUpResult.error);
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to create user account',
          timestamp: new Date().toISOString(),
        });
      }

      const targetUserId = signUpResult.data.user?.id || null;
      if (!targetUserId) {
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to get user ID from auth creation',
          timestamp: new Date().toISOString(),
        });
      }

      // Insert into user_profile
      const profileData = {
        id: targetUserId,
        email: memberData.email,
        first_name: memberData.first_name,
        last_name: memberData.last_name,
        phone: parseInt(memberData.phone),
        country: memberData.country,
        country_code: memberData.country_code,
        terms_conditions: true,
        workspace_id: workspaceId,
      };

      const { data: newProfile, error: profileError } = await this.supabaseService.insertUserProfile(profileData);
      
      if (profileError) {
        this.logger.error('Failed to create user profile:', profileError);
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to create user profile: ' + profileError.message,
          timestamp: new Date().toISOString(),
        });
      }

      const targetUserProfile = newProfile;

            // Check if the target user is already a member of this workspace
      const { data: existingMember, error: existingMemberError } = await this.supabaseService.getWorkspaceMember(
        workspaceId, 
        targetUserId
      );
      
      if (existingMember && !existingMemberError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User is already a member of this workspace',
          timestamp: new Date().toISOString(),
        });
      }

            // Insert into workspace_members
      const insertMemberData = {
        workspace_id: workspaceId,
        user_id: targetUserId,
        role: memberData.role,
        status: 'Active',
        reports_to: memberData.reports_to || null,
        waba_access: memberData.waba_access,
      };

      const { data: newMember, error: insertError } = await this.supabaseService.insertWorkspaceMember(insertMemberData);
      
      if (insertError) {
        this.logger.error('Failed to add workspace member:', insertError);
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to add member to workspace',
          timestamp: new Date().toISOString(),
        });
      }

      // Insert into automate_whatsapp_members if waba_access is true
      let automateMemberResult: { data: any; error: any } | null = null;
      // let roleCreated = false;
      
      if (memberData.waba_access) {
        // Validate that role_id is provided when waba_access is true
        if (!memberData.role_id) {
          return res.status(400).json({
            status: 'error',
            code: 400,
            message: 'Role ID is required when waba_access is true',
            timestamp: new Date().toISOString(),
          });
        }

        try {
          // Use the provided role_id instead of creating a new role
          automateMemberResult = await this.supabaseService.insertAutomateWhatsappMember({
            workspace_member_id: newMember.id,
            role_id: memberData.role_id,
            user_id: targetUserId,
            workspace_id: workspaceId,
            status: 'active'
          });
          
          if (automateMemberResult.error) {
            this.logger.error('Failed to add to automate whatsapp members:', automateMemberResult.error);
            return res.status(500).json({
              status: 'error',
              code: 500,
              message: 'Failed to add user to automate whatsapp members: ' + automateMemberResult.error.message,
              timestamp: new Date().toISOString(),
            });
          }
        } catch (error) {
          this.logger.error('Error in waba access setup:', error);
          return res.status(500).json({
            status: 'error',
            code: 500,
            message: 'Failed to setup waba access',
            timestamp: new Date().toISOString(),
          });
        }
      }

      return res.status(201).json({
        status: 'success',
        code: 201,
        message: 'User registered and added to workspace successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Add member to workspace failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async getWorkspaceMembers(req: any, res: any): Promise<Response> {
    try {
      const user = req.user;
      
      // Extract query parameters for search and pagination
      const { 
        search = '', 
        page = 1, 
        limit = 10, 
        sortBy = 'created_at', 
        sortOrder = 'desc' 
      } = req.query;

      // Validate pagination parameters
      const pageNum = Math.max(1, parseInt(page as string) || 1);
      const limitNum = Math.min(100, Math.max(1, parseInt(limit as string) || 10));
      const offset = (pageNum - 1) * limitNum;

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Get workspace members with search and pagination
      const { data: workspaceMembers, error: workspaceMembersError, count } = await this.supabaseService.getWorkspaceMembersWithSearch(
        userProfile.workspace_id,
        search as string,
        limitNum,
        offset,
        sortBy as string,
        sortOrder as string
      );
      
      if (workspaceMembersError) {
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to fetch workspace members',
          timestamp: new Date().toISOString()
        });
      }

      // Calculate pagination metadata
      const totalPages = Math.ceil((count || 0) / limitNum);
      const hasNextPage = pageNum < totalPages;
      const hasPrevPage = pageNum > 1;

      return res.status(200).json({
        status: 'success',
        code: 200,
        data: workspaceMembers || [],
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: count || 0,
          itemsPerPage: limitNum,
          hasNextPage,
          hasPrevPage,
          nextPage: hasNextPage ? pageNum + 1 : null,
          prevPage: hasPrevPage ? pageNum - 1 : null
        },
        filters: {
          search: search as string,
          sortBy: sortBy as string,
          sortOrder: sortOrder as string
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Get workspace members failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  

 
} 