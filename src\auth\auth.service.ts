import { Injectable, Logger, UnauthorizedException, ConflictException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';
import { SignUpDto, SignInDto, RefreshTokenDto, ChangePasswordDto } from '../dto/auth.dto';

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  workspace_id: number;
  custom_role: string;
  custom_role_id: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
  ) {}

  async signUp(signUpDto: SignUpDto) {
    try {
      // Check if user already exists in Supabase user_profile table
      const existingUser = await this.supabaseService.getUserProfileByEmail(signUpDto.email);
      if (existingUser) {
        throw new ConflictException('User with this email already exists');
      }
      // Create user in Supabase Auth
      const supabaseUserData: {
        first_name: string;
        last_name: string;
        phoneNumber?: string;
        countrycode?: string;
        country?: string;
      } = {
        first_name: signUpDto.first_name,
        last_name: signUpDto.last_name,
      };

      if (signUpDto.phoneNumber) supabaseUserData.phoneNumber = signUpDto.phoneNumber;
      if (signUpDto.countrycode) supabaseUserData.countrycode = signUpDto.countrycode;
      if (signUpDto.country) supabaseUserData.country = signUpDto.country;

      const supabaseResponse = await this.supabaseService.signUp(
        signUpDto.email,
        signUpDto.password,
        supabaseUserData
      );

      if (supabaseResponse.error) {
        // Generic Supabase error
        throw new BadRequestException(supabaseResponse.error.message || 'Signup failed. Please try again.');
      }

      // Insert user profile data into Supabase user_profile table
      const profileData = {
        id: supabaseResponse.data.user?.id || '',
        first_name: signUpDto.first_name,
        last_name: signUpDto.last_name,
        email: signUpDto.email,
        phone: parseInt(signUpDto.phoneNumber || '0'),
        country: signUpDto.country || 'Unknown',
        country_code: signUpDto.countrycode || '1',
        terms_conditions: signUpDto.terms_conditions,
        time_zone: 'Asia/Kolkata',
        first_visit: true,
        language: 'en'
      };

      const profileResult = await this.supabaseService.insertUserProfile(profileData);
      if (profileResult.error) {
        this.logger.error('Failed to insert user profile:', profileResult.error);
        throw new BadRequestException('Failed to insert user profile');
      }
      return {
        user: supabaseResponse.data.user,
        session: supabaseResponse.data.session,
        access_token: supabaseResponse.data.session?.access_token ?? '',
        refresh_token: supabaseResponse.data.session?.refresh_token ?? '',
      };
    } catch (error) {
      this.logger.error('Sign up failed:', error);
      
      // Re-throw specific exceptions as they are
      if (error instanceof ConflictException || 
          error instanceof BadRequestException || 
          error instanceof InternalServerErrorException) {
        throw error;
      }
      
      // Handle generic errors
      if (error instanceof Error) {
        throw new BadRequestException(error.message);
      }
      
      throw new InternalServerErrorException('Signup failed. Please try again.');
    }
  }

  async signIn(signInDto: SignInDto,res:any): Promise<AuthResponse> {
    try {
      // Authenticate with Supabase
      const supabaseResponse = await this.supabaseService.signIn(
        signInDto.email,
        signInDto.password
      );

      if (supabaseResponse.error) {
        throw new UnauthorizedException('Invalid credentials');
      }

      const userProfile = await this.supabaseService.getUserProfileByEmail(signInDto.email);
      if (!userProfile) {
        this.logger.warn(`User found in Supabase Auth but not in user_profile table: ${signInDto.email}`);
      }
     
      if(userProfile?.workspace_id){
        const {data:workspace,error:workspaceError}=await this.supabaseService.getWorkspaceById(userProfile?.workspace_id);
        const {data:workspacemember,error:workspacememberError}=await this.supabaseService.getWorkspaceMember(userProfile?.workspace_id,userProfile?.id);
        const {data:automate_whatsapp_member,error:automate_whatsapp_memberError}=await this.supabaseService.getAutomateWhatsappMember(userProfile?.id);
        const cards_access=workspace?.cards_access;
        const waba_access=workspace?.waba_access;
        return res.status(200).json({
          status: 'success',
          code: 200,
          message: 'Sign in successful',
          data: {
            access_token: supabaseResponse.data.session?.access_token ?? '',
            refresh_token: supabaseResponse.data.session?.refresh_token ?? '',
            custom_role_id: automate_whatsapp_member?.role_id || null,
            custom_role: automate_whatsapp_member?.automate_whatsapp_role?.name || null,
            expires_in: supabaseResponse.data.session?.expires_in || null,
            workspace_id: userProfile?.workspace_id || null,
            workspace_role:workspacemember?.role || null,
            waba_access:waba_access && workspacemember?.waba_access,
            cards_access:cards_access && workspacemember?.cards_access,
          },
          timestamp: new Date().toISOString(),
        })
       }
       else{
        return res.status(200).json({
          status: 'success',
          code: 200,
          message: 'user has not created workspace',
          data: {
            access_token: supabaseResponse.data.session?.access_token ?? '',
            refresh_token: supabaseResponse.data.session?.refresh_token ?? '',
            expires_in: supabaseResponse.data.session?.expires_in || null,
            workspace_id: null,
            waba_access:false,
            cards_access:false,
          },
          timestamp: new Date().toISOString(),
        })
       }
      
      
    } catch (error) {
      this.logger.error('Sign in failed:', error);
      console.log("error",error)
      // Re-throw specific exceptions as they are
      if (error instanceof UnauthorizedException || 
          error instanceof BadRequestException) {
        throw error;
      }
      
      // Handle generic errors
      if (error instanceof Error) {
        throw new UnauthorizedException('Sign in failed. Please check your credentials.');
      }
      
      throw new UnauthorizedException('Sign in failed. Please try again.');
    }
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto, res: any) {
    try {
      const supabaseResponse = await this.supabaseService.refreshSession(refreshTokenDto.refresh_token);

      if (supabaseResponse.error) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      const session = supabaseResponse.data.session;
      const user = supabaseResponse.data.user;

      let workspace_id: number | null = null;
      if (user?.email) {
        const userProfile = await this.supabaseService.getUserProfileByEmail(user.email);
        if (userProfile?.workspace_id) {
          workspace_id = userProfile.workspace_id;
          const automate_whatsapp_member = await this.supabaseService.getAutomateWhatsappMember(userProfile?.id);
          if (automate_whatsapp_member.data) {
            return res.status(200).json({
              status: 'success',
              code: 200,
              message: 'Token refreshed successfully',
              data: {
              access_token: session?.access_token ?? '',
              refresh_token: session?.refresh_token ?? refreshTokenDto.refresh_token ?? '',
              custom_role: automate_whatsapp_member.data?.role_id || null,
              custom_role_id: automate_whatsapp_member.data?.automate_whatsapp_role?.name || null,
              expires_in: session?.expires_in || null,
              workspace_id,
             },
             timestamp: new Date().toISOString(),
            });
          }
        }
      }

      return res.status(200).json({
        access_token: session?.access_token ?? '',
        refresh_token: session?.refresh_token ?? refreshTokenDto.refresh_token ?? '',
        expires_in: session?.expires_in || null,
        workspace_id,
      });
    } catch (error) {
      this.logger.error('Token refresh failed:', error);

      if (error instanceof UnauthorizedException || error instanceof BadRequestException) {
        throw error;
      }

      throw new UnauthorizedException('Token refresh failed. Please sign in again.');
    }
  }

  async signOut(token: string): Promise<void> {
    try {
      await this.supabaseService.signOut();
      this.logger.log('User signed out successfully');
    } catch (error) {
      this.logger.error('Sign out failed:', error);
      throw new InternalServerErrorException('Sign out failed. Please try again.');
    }
  }

  async resetPassword(email: string, redirectUrl: string): Promise<void> {
    try {
      const { error } = await this.supabaseService.resetPassword(email, redirectUrl);
      if (error) {
        if (error.code === 'user_not_found') {
          throw new BadRequestException('No user found with this email address.');
        }
        if (error.code === 'over_email_send_rate_limit') {
          throw new BadRequestException('Too many password reset attempts. Please wait before trying again.');
        }
        throw new BadRequestException(error.message || 'Password reset failed. Please try again.');
      }
      this.logger.log(`Password reset email sent to: ${email}`);
    } catch (error) {
      this.logger.error('Password reset failed:', error);
      
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      throw new InternalServerErrorException('Password reset failed. Please try again.');
    }
  }

  async validateToken(token: string): Promise<any> {
    try {
      const user = await this.supabaseService.getUser(token);
      if (!user) {
        throw new UnauthorizedException('Invalid or expired token');
      }
      return user;
    } catch (error) {
      this.logger.error('Token validation failed:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

 

  async updateUserProfile(userId: string, updates: any): Promise<any> {
    try {
      // Update in Supabase user_profile table
      const result = await this.supabaseService.updateUserProfile(userId, updates);
      
      if (result.error) {
        this.logger.error('Profile update failed:', result.error);
        throw new BadRequestException('Profile update failed. Please try again.');
      }

      return result.data;
    } catch (error) {
      this.logger.error('Profile update failed:', error);
      
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      throw new InternalServerErrorException('Profile update failed. Please try again.');
    }
  }

  async updateProfile(userId: string, updateProfileDto: any): Promise<any> {
    try {
      // Prepare update data
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Add only the fields that are provided
      if (updateProfileDto.first_name) updateData.first_name = updateProfileDto.first_name;
      if (updateProfileDto.last_name) updateData.last_name = updateProfileDto.last_name;
      if (updateProfileDto.phone) updateData.phone = parseInt(updateProfileDto.phone);
      if (updateProfileDto.country_code) updateData.country_code = updateProfileDto.country_code;
      if (updateProfileDto.country) updateData.country = updateProfileDto.country;
      if (updateProfileDto.time_zone) updateData.time_zone = updateProfileDto.time_zone;
      if (updateProfileDto.language) updateData.language = updateProfileDto.language;
      if(updateProfileDto.profile_image) updateData.profile_image = updateProfileDto.profile_image;

      // Update in Supabase user_profile table
      const result = await this.supabaseService.updateUserProfile(userId, updateData);
      
      if (result.error) {
        this.logger.error('Profile update failed:', result.error);
        throw new BadRequestException('Profile update failed. Please try again.');
      }

      return result.data;
    } catch (error) {
      this.logger.error('Profile update failed:', error);
      
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      throw new InternalServerErrorException('Profile update failed. Please try again.');
    }
  }

  async changePassword(userId: string, email: string | null | undefined, changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      if (!email) {
        throw new BadRequestException('User email is missing');
      }

      try {
        await this.supabaseService.signIn(email, changePasswordDto.current_password);
      } catch (err) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      const result = await this.supabaseService.updateUserPassword(userId, changePasswordDto.new_password);
      if (result.error) {
        this.logger.error('Password change failed:', result.error);
        throw new BadRequestException('Password change failed. Please try again.');
      }
    } catch (error) {
      this.logger.error('Change password failed:', error);
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException('Change password failed. Please try again.');
    }
  }
  async getUserProfile(userId: string): Promise<any> {
    try {
      const result = await this.supabaseService.getUserProfile(userId);
      return result;
    } catch (error) {
      this.logger.error('Failed to get user profile:', error);
      throw new InternalServerErrorException('Failed to get user profile. Please try again.');
    }
  }
  async getStepCount(userId: string): Promise<any> {
    try {
      
      // Get current step count
      const { data: currentUser, error: fetchError } = await this.supabaseService.getClient()
        .from('user_profile')
        .select('step_count')
        .eq('id', userId)
        .single();

      if (fetchError) {
        this.logger.error('Failed to get current step count:', fetchError);
        throw new InternalServerErrorException('Failed to get current step count');
      }

      // Increment step count with max limit of 3
      const currentStepCount = currentUser?.step_count || 0;
      const newStepCount = currentStepCount >= 3 ? 3 : currentStepCount + 1;

      // Update step count in database only if it changed
      const { data: updatedData, error: updateError } = await this.supabaseService.getClient()
        .from('user_profile')
        .update({ step_count: newStepCount })
        .eq('id', userId)
        .select('step_count')
        .single();

      if (updateError) {
        this.logger.error('Failed to update step count:', updateError);
        throw new InternalServerErrorException('Failed to update step count');
      }

      return {
        success: true,
        step_count: updatedData.step_count,
        message: 'Step count incremented successfully'
      };
    } catch (error) {
      this.logger.error('Step count operation failed:', error);
      
      if (error instanceof InternalServerErrorException) {
        throw error;
      }
      
      throw new InternalServerErrorException('Step count operation failed. Please try again.');
    }
  }
} 