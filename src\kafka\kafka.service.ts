import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { Kafka, Producer, Message } from 'kafkajs';
import { ConfigService } from '@nestjs/config';

export interface CampaignMessage {
  campaignId: string;
  contactId: string;
  phoneNumber: string;
  countryCode: string;
  templateId: string;
  phoneNumberId: string;
  variableMapping: Record<string, any>;
  workspaceId: number;
  userId: string;
  retryCount: number;
  priority: 'HIGH' | 'NORMAL' | 'LOW';
  scheduledAt?: Date;
}

export interface CampaignBatchMessage {
  campaignId: string;
  messages: CampaignMessage[];
  batchSize: number;
  totalContacts: number;
  workspaceId: number;
  userId: string;
}

@Injectable()
export class KafkaService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KafkaService.name);
  private kafka: Kafka;
  private producer: Producer;
  private isConnected = false;

  constructor(private configService: ConfigService) {
    this.kafka = new Kafka({
      clientId: 'whatsapp-campaign-producer',
      brokers: this.configService.get<string>('KAFKA_BROKERS', 'localhost:9092').split(','),
      retry: {
        initialRetryTime: 100,
        retries: 8,
      },
    });

    this.producer = this.kafka.producer({
      allowAutoTopicCreation: true,
      transactionTimeout: 30000,
    });
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect() {
    try {
      await this.producer.connect();
      this.isConnected = true;
      this.logger.log('Kafka producer connected successfully');
    } catch (error) {
      this.logger.error('Failed to connect to Kafka:', error);
      throw error;
    }
  }

  private async disconnect() {
    if (this.isConnected) {
      try {
        await this.producer.disconnect();
        this.isConnected = false;
        this.logger.log('Kafka producer disconnected');
      } catch (error) {
        this.logger.error('Error disconnecting from Kafka:', error);
      }
    }
  }

  async sendCampaignMessage(message: CampaignMessage): Promise<void> {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      const kafkaMessage: Message = {
        key: message.campaignId,
        value: JSON.stringify(message),
        headers: {
          'message-type': 'campaign-message',
          'priority': message.priority,
          'workspace-id': message.workspaceId.toString(),
        },
      };

      await this.producer.send({
        topic: 'campaign-messages',
        messages: [kafkaMessage],
      });

      this.logger.debug(`Campaign message sent for campaign ${message.campaignId} to ${message.phoneNumber}`);
    } catch (error) {
      this.logger.error(`Failed to send campaign message: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendCampaignBatch(batchMessage: CampaignBatchMessage): Promise<void> {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      const messages: Message[] = batchMessage.messages.map((msg) => ({
        key: msg.campaignId,
        value: JSON.stringify(msg),
        headers: {
          'message-type': 'campaign-message',
          'priority': msg.priority,
          'workspace-id': msg.workspaceId.toString(),
          'batch-id': `${batchMessage.campaignId}-${Date.now()}`,
        },
      }));
      await this.producer.send({
        topic: 'campaign-messages',
        messages,
      });

      this.logger.log(`Sent ${messages.length} campaign messages for campaign ${batchMessage.campaignId}`);
    } catch (error) {
      this.logger.error(`Failed to send campaign batch: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendScheduledCampaignMessage(message: CampaignMessage): Promise<void> {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      const kafkaMessage: Message = {
        key: message.campaignId,
        value: JSON.stringify(message),
        headers: {
          'message-type': 'scheduled-campaign-message',
          'priority': message.priority,
          'workspace-id': message.workspaceId.toString(),
          'scheduled-at': message.scheduledAt?.toISOString(),
        },
      };

      await this.producer.send({
        topic: 'scheduled-campaign-messages',
        messages: [kafkaMessage],
      });

      this.logger.debug(`Scheduled campaign message sent for campaign ${message.campaignId}`);
    } catch (error) {
      this.logger.error(`Failed to send scheduled campaign message: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendCampaignStatusUpdate(campaignId: string, status: string, stats: any): Promise<void> {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      const message: Message = {
        key: campaignId,
        value: JSON.stringify({
          campaignId,
          status,
          stats,
          timestamp: new Date().toISOString(),
        }),
        headers: {
          'message-type': 'campaign-status-update',
        },
      };

      await this.producer.send({
        topic: 'campaign-status-updates',
        messages: [message],
      });

      this.logger.debug(`Campaign status update sent for campaign ${campaignId}: ${status}`);
    } catch (error) {
      this.logger.error(`Failed to send campaign status update: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendRetryMessage(message: CampaignMessage): Promise<void> {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      const kafkaMessage: Message = {
        key: message.campaignId,
        value: JSON.stringify(message),
        headers: {
          'message-type': 'retry-campaign-message',
          'priority': 'HIGH',
          'workspace-id': message.workspaceId.toString(),
          'retry-count': message.retryCount.toString(),
        },
      };

      await this.producer.send({
        topic: 'campaign-retry-messages',
        messages: [kafkaMessage],
      });

      this.logger.debug(`Retry message sent for campaign ${message.campaignId} to ${message.phoneNumber}`);
    } catch (error) {
      this.logger.error(`Failed to send retry message: ${error.message}`, error.stack);
      throw error;
    }
  }
}
