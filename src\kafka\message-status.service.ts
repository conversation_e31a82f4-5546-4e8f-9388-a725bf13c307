import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Campaign, CampaignDocument } from '../schema/campaign.schema';

@Injectable()
export class MessageStatusService {
  private readonly logger = new Logger(MessageStatusService.name);

  constructor(
    @InjectModel(Campaign.name) private campaignModel: Model<CampaignDocument>,
  ) {}

  async updateMessageStatus(
    campaignId: string,
    phoneNumber: string,
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED',
    messageId?: string,
    errorMessage?: string,
  ): Promise<void> {
    try {
      const updateData: any = {
        $inc: {
          [`delivery_stats.${status.toLowerCase()}`]: 1,
          pending_count: -1,
        },
      };

      // Update message log
      updateData.$set = {
        [`message_logs.$[elem].status`]: status,
        [`message_logs.$[elem].${status.toLowerCase()}_at`]: new Date(),
      };

      if (messageId) {
        updateData.$set[`message_logs.$[elem].message_id`] = messageId;
      }

      if (errorMessage) {
        updateData.$set[`message_logs.$[elem].error_message`] = errorMessage;
      }

      await this.campaignModel.updateOne(
        { _id: campaignId, 'message_logs.phone': phoneNumber },
        updateData,
        {
          arrayFilters: [{ 'elem.phone': phoneNumber }],
        },
      );

      this.logger.debug(`Updated message status for campaign ${campaignId}, phone ${phoneNumber}: ${status}`);
    } catch (error) {
      this.logger.error(`Failed to update message status: ${error.message}`, error.stack);
    }
  }
}
