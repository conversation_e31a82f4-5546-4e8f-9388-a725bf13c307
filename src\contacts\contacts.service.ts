import { Injectable, UnauthorizedException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ContactDto } from 'src/dto/contacts.dto';
import { Contact } from 'src/schema/contacts.schema';
import { SupabaseService } from 'src/supabase/supabase.service';
import { UpdateContactDto } from 'src/dto/update-contact.dto';
// Simple CSV parsing utilities to avoid external dependency issues

function splitCsvLine(line: string): string[] {
    const values: string[] = [];
    let current = '';
    let inQuotes = false;
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                current += '"';
                i++;
            } else {
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            values.push(current);
            current = '';
        } else {
            current += char;
        }
    }
    values.push(current);
    return values.map(v => v.trim());
}

function parseCsv(buffer: Buffer): any[] {
    const text = buffer.toString('utf8');
    const lines = text.split(/\r?\n/).filter(l => l.trim().length > 0);
    if (lines.length === 0) return [];
    const header = splitCsvLine(lines[0]);
    const records: any[] = [];
    for (let i = 1; i < lines.length; i++) {
        const cols = splitCsvLine(lines[i]);
        const record: any = {};
        for (let c = 0; c < header.length; c++) {
            const key = header[c];
            const val = cols[c] ?? '';
            record[key] = val;
        }
        records.push(record);
    }
    return records;
}


@Injectable()
export class ContactsService {
    private readonly logger = new Logger(ContactsService.name);
    constructor(
        @InjectModel(Contact.name) private contactModel: Model<Contact>,
        private readonly supabaseService: SupabaseService,
    ) { }

   
    async createContact(contactDto: ContactDto, req: any, res: any) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }
        const {data:userProfile,error:userProfileError}=await this.supabaseService.getUserProfile(user.id);
        if(userProfileError){
            this.logger.error(`Error getting user profile: ${userProfileError}`);
            throw new BadRequestException('User not found');
        }
        if(!userProfile.workspace_id){
            this.logger.error(`User profile does not have a workspace id`);
            throw new BadRequestException('User not found');
        }
        const workspaceId=userProfile.workspace_id;
        // Proactive duplicate check if phoneNumber provided
        if (contactDto.phoneNumber) {
            const existing = await this.contactModel.findOne({ workspaceId, phoneNumber: contactDto.phoneNumber });
            if (existing) {
                return res.status(409).json({
                    status: 'error',
                    code: 409,
                    message: 'Contact with this phone number already exists in this workspace',
                    timestamp: new Date().toISOString(),
                });
            }
        }
        const payload = {
            ...contactDto,
            createdBy: user.id,
            workspaceId: workspaceId,
        }
        const contact = new this.contactModel(payload);
        let savedContact;
        try {
            savedContact = await contact.save();
        } catch (error: any) {
            // Handle duplicate key error from Mongo unique index
            if (error?.code === 11000) {
                return res.status(409).json({
                    status: 'error',
                    code: 409,
                    message: 'Contact with this phone number already exists in this workspace',
                    timestamp: new Date().toISOString(),
                });
            }
            this.logger.error('Failed to create contact', error);
            throw new BadRequestException('Failed to create contact');
        }
        this.logger.log(`Contact created successfully: ${savedContact}`);
        return res.status(201).json({
            status: 'success',
            code: 201,
            message: 'Contact created successfully',
            data: savedContact,
            
            timestamp: new Date().toISOString()
        });
    }


    async getContactsForWorkspace(req: any, res: any) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }
        const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
        if (userProfileError) {
            this.logger.error(`Error getting user profile: ${userProfileError}`);
            throw new BadRequestException('User not found');
        }
        if (!userProfile.workspace_id) {
            this.logger.error(`User profile does not have a workspace id`);
            throw new BadRequestException('User not found');
        }
        const workspaceId = userProfile.workspace_id;

        const page = parseInt(req.query?.page) > 0 ? parseInt(req.query.page) : 1;
        const limit = parseInt(req.query?.limit) > 0 ? parseInt(req.query.limit) : 10;
        const skip = (page - 1) * limit;

        // Build dynamic filters
        const andConditions: any[] = [{ workspaceId }];

        // Basic field filters (case-insensitive for strings)
        const stringFields = ['firstName', 'lastName', 'chatName', 'email', 'phoneNumber', 'countryCode', 'source'];
        for (const field of stringFields) {
            const value = req.query?.[field as any];
            if (typeof value !== 'undefined' && value !== null && String(value).length > 0) {
                andConditions.push({ [field]: { $regex: new RegExp(String(value), 'i') } });
            }
        }

        // Subscribed filter
        if (typeof req.query?.subscribed !== 'undefined') {
            const v = String(req.query.subscribed).toLowerCase();
            const boolVal = v === 'true' || v === '1' || v === 'yes';
            andConditions.push({ subscribed: boolVal });
        }

        // Tags filter: tagsId or tags (comma separated). tagsMode: 'any' (default) | 'all'
        const tagsParam = (req.query?.tagsId || req.query?.tags) as string | undefined;
        if (tagsParam) {
            const tags = String(tagsParam)
                .split(',')
                .map((s) => s.trim())
                .filter((s) => s);
            if (tags.length > 0) {
                const tagsMode = String(req.query?.tagsMode || 'any').toLowerCase();
                if (tagsMode === 'all') {
                    andConditions.push({ tagsId: { $all: tags } });
                } else {
                    andConditions.push({ tagsId: { $in: tags } });
                }
            }
        }

        // Custom field single-pair filter
        const customFieldId = req.query?.customFieldId as string | undefined;
        const customFieldValue = req.query?.customFieldValue as string | undefined;
        if (customFieldId && typeof customFieldValue !== 'undefined') {
            const cfCond: any = { fieldId: customFieldId };
            // If value is numeric-like or boolean-like, still allow regex match for broad search
            cfCond.value = { $regex: new RegExp(String(customFieldValue), 'i') };
            andConditions.push({ customFields: { $elemMatch: cfCond } });
        }

        // Multiple custom field pairs via JSON: customFieldsFilters=[{fieldId,value},...]; cfMode: 'all'|'any'
        if (req.query?.customFieldsFilters) {
            try {
                const parsed = JSON.parse(String(req.query.customFieldsFilters));
                if (Array.isArray(parsed) && parsed.length > 0) {
                    const cfMode = String(req.query?.cfMode || 'all').toLowerCase();
                    const elems = parsed
                        .filter((p) => p && p.fieldId && typeof p.value !== 'undefined')
                        .map((p) => ({
                            customFields: {
                                $elemMatch: {
                                    fieldId: p.fieldId,
                                    value: { $regex: new RegExp(String(p.value), 'i') },
                                },
                            },
                        }));
                    if (elems.length > 0) {
                        if (cfMode === 'any') {
                            andConditions.push({ $or: elems });
                        } else {
                            andConditions.push(...elems);
                        }
                    }
                }
            } catch (e) {
                // Ignore malformed JSON; no custom field filters applied
            }
        }

        // Free-text search across common fields and customFields.value
        const q = req.query?.q as string | undefined;
        if (q && q.trim().length > 0) {
            const rx = new RegExp(q.trim(), 'i');
            andConditions.push({
                $or: [
                    { firstName: { $regex: rx } },
                    { lastName: { $regex: rx } },
                    { chatName: { $regex: rx } },
                    { email: { $regex: rx } },
                    { phoneNumber: { $regex: rx } },
                    { 'customFields.value': { $regex: rx } },
                ],
            });
        }

        const mongoFilter = andConditions.length > 1 ? { $and: andConditions } : andConditions[0];

        const [contacts, total] = await Promise.all([
            this.contactModel
                .find(mongoFilter)
                .skip(skip)
                .limit(limit)
                .sort({ createdAt: -1 })
                .exec(),
            this.contactModel.countDocuments(mongoFilter),
        ]);

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'Contacts fetched successfully',
            data: contacts,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            },
            timestamp: new Date().toISOString(),
        });
    }

    async getContactsForUser(req: any, res: any) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        const page = parseInt(req.query?.page) > 0 ? parseInt(req.query.page) : 1;
        const limit = parseInt(req.query?.limit) > 0 ? parseInt(req.query.limit) : 10;
        const skip = (page - 1) * limit;

        // Build dynamic filters (scoped to contacts created by the user)
        const andConditions: any[] = [{ createdBy: user.id }];

        // Basic field filters (case-insensitive for strings)
        const stringFields = ['firstName', 'lastName', 'chatName', 'email', 'phoneNumber', 'countryCode', 'source'];
        for (const field of stringFields) {
            const value = req.query?.[field as any];
            if (typeof value !== 'undefined' && value !== null && String(value).length > 0) {
                andConditions.push({ [field]: { $regex: new RegExp(String(value), 'i') } });
            }
        }

        // Subscribed filter
        if (typeof req.query?.subscribed !== 'undefined') {
            const v = String(req.query.subscribed).toLowerCase();
            const boolVal = v === 'true' || v === '1' || v === 'yes';
            andConditions.push({ subscribed: boolVal });
        }

        // Tags filter: tagsId or tags (comma separated). tagsMode: 'any' (default) | 'all'
        const tagsParam = (req.query?.tagsId || req.query?.tags) as string | undefined;
        if (tagsParam) {
            const tags = String(tagsParam)
                .split(',')
                .map((s) => s.trim())
                .filter((s) => s);
            if (tags.length > 0) {
                const tagsMode = String(req.query?.tagsMode || 'any').toLowerCase();
                if (tagsMode === 'all') {
                    andConditions.push({ tagsId: { $all: tags } });
                } else {
                    andConditions.push({ tagsId: { $in: tags } });
                }
            }
        }

        // Custom field single-pair filter
        const customFieldId = req.query?.customFieldId as string | undefined;
        const customFieldValue = req.query?.customFieldValue as string | undefined;
        if (customFieldId && typeof customFieldValue !== 'undefined') {
            const cfCond: any = { fieldId: customFieldId };
            cfCond.value = { $regex: new RegExp(String(customFieldValue), 'i') };
            andConditions.push({ customFields: { $elemMatch: cfCond } });
        }

        // Multiple custom field pairs via JSON: customFieldsFilters=[{fieldId,value},...]; cfMode: 'all'|'any'
        if (req.query?.customFieldsFilters) {
            try {
                const parsed = JSON.parse(String(req.query.customFieldsFilters));
                if (Array.isArray(parsed) && parsed.length > 0) {
                    const cfMode = String(req.query?.cfMode || 'all').toLowerCase();
                    const elems = parsed
                        .filter((p) => p && p.fieldId && typeof p.value !== 'undefined')
                        .map((p) => ({
                            customFields: {
                                $elemMatch: {
                                    fieldId: p.fieldId,
                                    value: { $regex: new RegExp(String(p.value), 'i') },
                                },
                            },
                        }));
                    if (elems.length > 0) {
                        if (cfMode === 'any') {
                            andConditions.push({ $or: elems });
                        } else {
                            andConditions.push(...elems);
                        }
                    }
                }
            } catch (e) {
                // Ignore malformed JSON; no custom field filters applied
            }
        }

        // Free-text search across common fields and customFields.value
        const q = req.query?.q as string | undefined;
        if (q && q.trim().length > 0) {
            const rx = new RegExp(q.trim(), 'i');
            andConditions.push({
                $or: [
                    { firstName: { $regex: rx } },
                    { lastName: { $regex: rx } },
                    { chatName: { $regex: rx } },
                    { email: { $regex: rx } },
                    { phoneNumber: { $regex: rx } },
                    { 'customFields.value': { $regex: rx } },
                ],
            });
        }

        const mongoFilter = andConditions.length > 1 ? { $and: andConditions } : andConditions[0];

        const [contacts, total] = await Promise.all([
            this.contactModel
                .find(mongoFilter)
                .skip(skip)
                .limit(limit)
                .sort({ createdAt: -1 })
                .exec(),
            this.contactModel.countDocuments(mongoFilter),
        ]);

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'User contacts fetched successfully',
            data: contacts,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            },
            timestamp: new Date().toISOString(),
        });
    }

    async deleteContact(id: string) {
        return await this.contactModel.findByIdAndDelete(id);
    }

    async updateContact(id: string, updateDto: UpdateContactDto, req: any, res: any) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }
        const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
        if (userProfileError) {
            this.logger.error(`Error getting user profile: ${userProfileError}`);
            throw new BadRequestException('User not found');
        }
        if (!userProfile.workspace_id) {
            this.logger.error(`User profile does not have a workspace id`);
            throw new BadRequestException('User not found');
        }
        const workspaceId = userProfile.workspace_id;

        // If updating phone number, check uniqueness within workspace
        if (updateDto.phoneNumber) {
            const dup = await this.contactModel.findOne({
                _id: { $ne: id },
                workspaceId,
                phoneNumber: updateDto.phoneNumber,
            });
            if (dup) {
                return res.status(409).json({
                    status: 'error',
                    code: 409,
                    message: 'Another contact with this phone number already exists in this workspace',
                    timestamp: new Date().toISOString(),
                });
            }
        }

        try {
            const updated = await this.contactModel.findOneAndUpdate(
                { _id: id, workspaceId },
                { $set: updateDto },
                { new: true }
            );
            if (!updated) {
                return res.status(404).json({
                    status: 'error',
                    code: 404,
                    message: 'Contact not found in this workspace',
                    timestamp: new Date().toISOString(),
                });
            }
            return res.status(200).json({
                status: 'success',
                code: 200,
                message: 'Contact updated successfully',
                data: updated,
                timestamp: new Date().toISOString(),
            });
        } catch (error: any) {
            if (error?.code === 11000) {
                return res.status(409).json({
                    status: 'error',
                    code: 409,
                    message: 'Another contact with this phone number already exists in this workspace',
                    timestamp: new Date().toISOString(),
                });
            }
            this.logger.error('Failed to update contact', error);
            throw new BadRequestException('Failed to update contact');
        }
    }

    async importContactsCsv(
        file: any,
        mapping: Record<string, string>,
        req: any,
        res: any,
    ) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }
        if (!file || !file.buffer || file.size === 0) {
            throw new BadRequestException('CSV file is required');
        }
        const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
        if (userProfileError) {
            this.logger.error(`Error getting user profile: ${userProfileError}`);
            throw new BadRequestException('User not found');
        }
        if (!userProfile.workspace_id) {
            this.logger.error(`User profile does not have a workspace id`);
            throw new BadRequestException('User not found');
        }
        const workspaceId = userProfile.workspace_id;

        // Parse CSV
        let records: any[] = [];
        try {
            records = parseCsv(file.buffer);
        } catch (e) {
            this.logger.error('Failed to parse CSV', e);
            throw new BadRequestException('Invalid CSV format');
        }
        if (!Array.isArray(records) || records.length === 0 || Object.keys(records[0] || {}).length === 0) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'Invalid or empty CSV (missing header or rows)',
                timestamp: new Date().toISOString(),
            });
        }

        // Validate mapping presence and that columns exist in header
        if (!mapping || typeof mapping !== 'object' || Object.keys(mapping).length === 0) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'mapping is required and must map fields to CSV headers',
                timestamp: new Date().toISOString(),
            });
        }
		const headerSet = new Set(Object.keys(records[0]));
		// Ignore `source` from header validation since it is hardcoded
		const missingHeaders = Object.entries(mapping)
			.filter(([field, csvCol]) => field !== 'source' && !headerSet.has(String(csvCol)))
			.map(([_, csvCol]) => csvCol);
        if (missingHeaders.length) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'Some mapped CSV columns were not found in the CSV header',
                details: { missingCsvColumns: missingHeaders, header: Array.from(headerSet) },
                timestamp: new Date().toISOString(),
            });
        }

        // Require mapping to include required fields
        const requiredFields = ['countryCode', 'phoneNumber'];
        const missingRequired = requiredFields.filter(f => !(f in mapping));
        if (missingRequired.length) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `mapping missing required fields: ${missingRequired.join(', ')}`,
                timestamp: new Date().toISOString(),
            });
        }

        // Mapping: map CSV columns to our fields
        // Expected mapping keys can include: firstName, lastName, chatName, email, phoneNumber, countryCode, source, subscribed, tagsId
        const results = { success: 0, failed: 0, errors: [] as any[] };

        // Preload existing phone numbers in this CSV to de-dup within file
        const seenInFile = new Set<string>();

        // Build documents
        const docs: any[] = [];
        for (let i = 0; i < records.length; i++) {
            const row = records[i];
            try {
                const doc: any = { createdBy: user.id, workspaceId, subscribed: true };
				// Apply mapping (ignore `source` as it is hardcoded)
				for (const [field, csvColumn] of Object.entries(mapping || {})) {
					if (field === 'source') continue;
					const value = row[csvColumn];
					if (value !== undefined && value !== null && value !== '') {
						doc[field] = value;
					}
				}
				// Hardcode source
				doc.source = 'import';

				// Validate required fields
				if (!doc.countryCode || !doc.phoneNumber) {
					throw new Error('Missing required fields: countryCode, phoneNumber');
				}

                // Normalize boolean for subscribed
                if (typeof doc.subscribed === 'string') {
                    const v = String(doc.subscribed).toLowerCase();
                    doc.subscribed = v === 'true' || v === '1' || v === 'yes';
                }

                // Clean/validate phone number
                if (doc.phoneNumber) {
                    const pn = String(doc.phoneNumber).replace(/\D+/g, '');
                    if (pn.length < 10) {
                        throw new Error('Invalid phone number');
                    }
                    if (seenInFile.has(pn)) {
                        throw new Error('Duplicate phone number in CSV');
                    }
                    seenInFile.add(pn);
                    doc.phoneNumber = pn;
                }

                // Convert tags if comma separated
                if (doc.tagsId && typeof doc.tagsId === 'string') {
                    doc.tagsId = String(doc.tagsId)
                        .split(',')
                        .map((s: string) => s.trim())
                        .filter((s: string) => s);
                }

                docs.push(doc);
            } catch (error: any) {
                results.failed++;
                results.errors.push({ index: i + 1, error: error.message, row });
            }
        }

        if (docs.length === 0) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'No valid rows to import',
                summary: results,
                timestamp: new Date().toISOString(),
            });
        }

        // Optimization: pre-check duplicates in DB in one query
        const phoneNumbers = docs
            .map(d => d.phoneNumber)
            .filter((pn: string | undefined) => !!pn);
        if (phoneNumbers.length) {
            const existing = await this.contactModel
                .find({ workspaceId, phoneNumber: { $in: phoneNumbers } }, { phoneNumber: 1 })
                .lean();
            const existingSet = new Set(existing.map((e: any) => e.phoneNumber));
            // filter out duplicates against DB
            const filtered: any[] = [];
            for (const d of docs) {
                if (d.phoneNumber && existingSet.has(d.phoneNumber)) {
                    results.failed++;
                    results.errors.push({ error: 'Duplicate phone number in workspace', phoneNumber: d.phoneNumber });
                } else {
                    filtered.push(d);
                }
            }
            docs.length = 0;
            docs.push(...filtered);
        }

        if (docs.length === 0) {
            return res.status(200).json({
                status: 'success',
                code: 200,
                message: 'No new contacts to import',
                summary: results,
                timestamp: new Date().toISOString(),
            });
        }

        // Batch insert using insertMany with ordered:false to continue on errors
        try {
            const inserted = await this.contactModel.insertMany(docs, { ordered: false });
            results.success += inserted.length;
        } catch (error: any) {
            // Handle partial failures (e.g., race-condition duplicates)
            if (Array.isArray(error?.writeErrors)) {
                for (const we of error.writeErrors) {
                    if (we.code === 11000) {
                        results.failed++;
                        results.errors.push({ error: 'Duplicate phone number in workspace', details: we.errmsg });
                    } else {
                        results.failed++;
                        results.errors.push({ error: 'Insert failed', details: we.errmsg });
                    }
                }
                if (error.result?.result?.nInserted) {
                    results.success += error.result.result.nInserted;
                }
            } else {
                this.logger.error('Bulk insert failed', error);
                throw new BadRequestException('Bulk import failed');
            }
        }

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'Bulk import completed',
            summary: results,
            timestamp: new Date().toISOString(),
        });
    }
}
