import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { KafkaService } from './kafka.service';
import { KafkaConsumerService } from './kafka-consumer.service';
import { MessageStatusService } from './message-status.service';
import { TemplateModule } from '../template/template.module';
import { MetaApiModule } from '../meta-api/meta-api.module';
import { Campaign, CampaignSchema } from '../schema/campaign.schema';
import { SupabaseModule } from 'src/supabase/supabase.module';

@Module({
  imports: [
    TemplateModule, 
    MetaApiModule,
    SupabaseModule,
    MongooseModule.forFeature([
      { name: Campaign.name, schema: CampaignSchema }
    ])
  ],
  providers: [KafkaService, KafkaConsumerService, MessageStatusService],
  exports: [KafkaService, KafkaConsumerService],
})
export class KafkaModule {}
