import { AiService } from 'src/ai/ai.service';
import { 
    Injectable, 
    UnauthorizedException, 
    BadRequestException, 
    NotFoundException, 
    Logger, 
    InternalServerErrorException 
} from '@nestjs/common';
import { Response } from 'express';
import { SupabaseService } from 'src/supabase/supabase.service';
import { MetaApiService } from 'src/meta-api/meta-api.service';
import { 
    CreateTemplateDto, 
    UpdateTemplateDto, 
    TemplateQueryDto, 
    CreateAiTemplateDto, 
    CreateVoiceTemplateDto 
} from 'src/dto/template.dto';
import { ConfigService } from '@nestjs/config';

// Types and Interfaces
interface UserContext {
    id: string;
    email?: string;
}

interface UserProfile {
    id: string;
    workspace_id: string;
}

interface MetaCredentials {
    access_token: string;
    whatsapp_business_id: string;
    status: string;
    created_by: string;
}

interface StandardResponse {
    status: 'success' | 'error';
    code: number;
    message: string;
    data?: any;
    error?: any;
    timestamp: string;
}

interface PaginationParams {
    page: number;
    limit: number;
    skip: number;
}

interface MetaIntegrationResult {
    success: boolean;
    response?: any;
    error?: string;
}

@Injectable()
export class TemplateService {
    private readonly logger = new Logger(TemplateService.name);
    private readonly DEFAULT_PAGE_SIZE = 10;
    private readonly MAX_PAGE_SIZE = 100;
    
    constructor(
        private readonly supabaseService: SupabaseService,
        private readonly metaApiService: MetaApiService,
        private readonly configService: ConfigService,
        private readonly aiService: AiService
    ) {}

    // ==================== UTILITY METHODS ====================

    /**
     * Validates and extracts user context from request
     * @param req - Express request object
     * @returns User context
     * @throws UnauthorizedException if user is invalid
     */
    private validateUserContext(req: any): UserContext {
        const user = req.user;
        if (!user?.id) {
            throw new UnauthorizedException('User authentication required');
        }
        return {
            id: user.id,
            email: user.email
        };
    }

    /**
     * Retrieves and validates user profile
     * @param userId - User ID
     * @returns User profile data
     * @throws BadRequestException if profile not found
     */
    private async getUserProfile(userId: string): Promise<UserProfile> {
        const { data, error } = await this.supabaseService.getUserProfile(userId);
        
        if (error || !data) {
            this.logger.error(`Failed to get user profile for ${userId}:`, error);
            throw new BadRequestException('User profile not found');
        }
        
        return {
            id: data.id,
            workspace_id: data.workspace_id
        };
    }

    /**
     * Creates standardized success response
     * @param data - Response data
     * @param message - Success message
     * @param code - HTTP status code
     * @returns Standardized response object
     */
    private createSuccessResponse(
        data: any, 
        message: string = 'Operation completed successfully',
        code: number = 200
    ): StandardResponse {
        return {
            status: 'success',
            code,
            message,
            data,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Creates standardized error response
     * @param message - Error message
     * @param error - Error details
     * @param code - HTTP status code
     * @returns Standardized error response
     */
    private createErrorResponse(
        message: string, 
        error?: any, 
        code: number = 400
    ): StandardResponse {
        return {
            status: 'error',
            code,
            message,
            error: error?.message || error,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Sends JSON response with proper status code
     * @param res - Express response object
     * @param responseData - Response data
     */
    private sendResponse(res: Response, responseData: StandardResponse): void {
        res.status(responseData.code).json(responseData);
    }

    /**
     * Parses and validates pagination parameters
     * @param query - Query parameters
     * @returns Pagination parameters
     */
    private parsePaginationParams(query: TemplateQueryDto): PaginationParams {
        const page = Math.max(1, parseInt(query.page || '1', 10));
        const limit = Math.min(
            this.MAX_PAGE_SIZE, 
            Math.max(1, parseInt(query.limit || this.DEFAULT_PAGE_SIZE.toString(), 10))
        );
        const skip = (page - 1) * limit;
        
        return { page, limit, skip };
    }

    /**
     * Builds Supabase query with filters
     * @param baseQuery - Base Supabase query
     * @param filters - Filter parameters
     * @returns Filtered query
     */
    private applyQueryFilters(baseQuery: any, filters: TemplateQueryDto): any {
        let query = baseQuery;

        if (filters.search) {
            query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
        }

        if (filters.category) {
            query = query.eq('category', filters.category);
        }

        if (filters.type) {
            query = query.eq('type', filters.type);
        }

        if (filters.waba_id) {
            query = query.eq('waba_id', filters.waba_id);
        }

        if (filters.status) {
            query = query.eq('meta_template_status', filters.status);
        }

        if (filters.language) {
            query = query.eq('language', filters.language);
        }

        return query;
    }

    /**
     * Checks if template with same name exists for user
     * @param templateDto - Template data
     * @param workspaceId - Workspace ID
     * @returns True if template exists
     */
    private async templateExists(templateDto: CreateTemplateDto, workspaceId: string): Promise<boolean> {
        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .select('id')
            .eq('name', templateDto.name)
            .eq('language', templateDto.language || 'en')
            .eq('waba_id', templateDto.waba_id)
            .eq('workspace_id', workspaceId)
            .maybeSingle();

        if (error && error.code !== 'PGRST116') {
            this.logger.error('Error checking template existence:', error);
            throw new BadRequestException('Failed to check template existence');
        }

        return !!data;
    }

    /**
     * Retrieves Meta API credentials for user and WABA
     * @param userId - User ID
     * @param wabaId - WhatsApp Business Account ID
     * @returns Meta credentials or null
     */
    private async getMetaCredentials(userId: string, wabaId: string): Promise<MetaCredentials | null> {
        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('created_by', userId)
            .eq('status', 'Active')
            .eq('whatsapp_business_id', wabaId)
            .maybeSingle();

        if (error && error.code !== 'PGRST116') {
            this.logger.warn(`Failed to get Meta credentials for user ${userId}:`, error);
        }

        return data;
    }

    /**
     * Handles Meta API integration for template operations
     * @param template - Template data
     * @param credentials - Meta API credentials
     * @param operation - Operation type
     * @param metaTemplateData - Meta template data
     * @returns Integration result
     */
    private async handleMetaIntegration(
        template: any,
        credentials: MetaCredentials,
        operation: 'create' | 'update',
        metaTemplateData: any
    ): Promise<MetaIntegrationResult> {
        try {
            let response;

            if (operation === 'update' && template.meta_template_id) {
                this.logger.log(`Updating Meta template: ${template.meta_template_id}`);
                response = await this.metaApiService.updateTemplate(
                    template.meta_template_id,
                    credentials.access_token,
                    metaTemplateData
                );
            } else {
                this.logger.log(`Creating new Meta template for WABA: ${credentials.whatsapp_business_id}`);
                response = await this.metaApiService.createTemplate(
                    credentials.whatsapp_business_id,
                    credentials.access_token,
                    metaTemplateData
                );
            }

            this.logger.log(`Meta template ${operation} successful:`, response?.id || 'success');
            return { success: true, response };

        } catch (error) {
            this.logger.error(`Meta template ${operation} failed:`, error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Prepares template payload for database insertion
     * @param templateDto - Template DTO
     * @param userProfile - User profile
     * @param isDraft - Whether template is draft
     * @returns Template payload
     */
    private prepareTemplatePayload(
        templateDto: CreateTemplateDto, 
        userProfile: UserProfile, 
        isDraft: boolean = false
    ): any {
        const { category, ...templateData } = templateDto;
        const now = new Date().toISOString();

        return {
            ...templateData,
            workspace_id: userProfile.workspace_id,
            created_by: userProfile.id,
            is_draft: isDraft,
            language: templateDto.language || 'en',
            created_at: now,
            updated_at: now
        };
    }

    /**
     * Updates template with Meta integration results
     * @param templateId - Template ID
     * @param metaResult - Meta integration result
     * @param metaTemplateData - Meta template data
     */
    private async updateTemplateWithMetaData(
        templateId: string, 
        metaResult: MetaIntegrationResult, 
        metaTemplateData: any
    ): Promise<void> {
        if (!metaResult.success || !metaResult.response) return;

        const updateData = {
            meta_template_id: metaResult.response.id,
            meta_template_status: metaResult.response.status,
            meta_template_category: metaTemplateData.category,
            updated_at: new Date().toISOString()
        };

        const { error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_templates')
            .update(updateData)
            .eq('id', templateId);

        if (error) {
            this.logger.error(`Failed to update template ${templateId} with Meta data:`, error);
        }
    }

    // ==================== TEMPLATE CRUD OPERATIONS ====================

    /**
     * Creates a new WhatsApp template
     * @param templateDto - Template creation data
     * @param req - Express request
     * @param res - Express response
     */
    async createTemplate(templateDto: CreateTemplateDto, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);
            const userProfile = await this.getUserProfile(user.id);

            // Check for duplicate template
            const exists = await this.templateExists(templateDto, userProfile.workspace_id);
            if (exists) {
                return this.sendResponse(res, this.createErrorResponse(
                    'Template with same name and language already exists',
                    null,
                    409
                ));
            }

            // Create template in database
            const templatePayload = this.prepareTemplatePayload(templateDto, userProfile);
            const { data: newTemplate, error: createError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .insert([templatePayload])
                .select()
                .single();

            if (createError) {
                throw new BadRequestException(`Failed to create template: ${createError.message}`);
            }

            // Attempt Meta integration
            let metaIntegrationSuccess = false;
            const metaCredentials = await this.getMetaCredentials(user.id, templateDto.waba_id);
            
            if (metaCredentials) {
                const metaTemplateData = this.metaApiService.convertToMetaFormat(templateDto);
                const metaResult = await this.handleMetaIntegration(
                    newTemplate,
                    metaCredentials,
                    'create',
                    metaTemplateData
                );

                if (metaResult.success) {
                    await this.updateTemplateWithMetaData(newTemplate.id, metaResult, metaTemplateData);
                    metaIntegrationSuccess = true;
                }
            }

            const message = metaIntegrationSuccess 
                ? 'Template created successfully with Meta integration'
                : 'Template created successfully (Meta integration skipped)';

            this.sendResponse(res, this.createSuccessResponse(
                { ...newTemplate, meta_integration_success: metaIntegrationSuccess },
                message,
                201
            ));

        } catch (error) {
            this.logger.error('Template creation failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to create template');
        }
    }

    /**
     * Creates a draft template (local only)
     * @param templateDto - Template creation data
     * @param req - Express request
     * @param res - Express response
     */
    async createDraftTemplate(templateDto: CreateTemplateDto, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);
            const userProfile = await this.getUserProfile(user.id);

            const templatePayload = this.prepareTemplatePayload(templateDto, userProfile, true);
            const { data: newTemplate, error: createError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .insert([templatePayload])
                .select()
                .single();

            if (createError) {
                throw new BadRequestException(`Failed to create draft template: ${createError.message}`);
            }

            this.sendResponse(res, this.createSuccessResponse(
                newTemplate,
                'Draft template created successfully',
                201
            ));

        } catch (error) {
            this.logger.error('Draft template creation failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to create draft template');
        }
    }

    /**
     * Updates an existing template
     * @param templateId - Template ID
     * @param updateTemplateDto - Update data
     * @param req - Express request
     * @param res - Express response
     */
    async updateTemplate(
        templateId: string, 
        updateTemplateDto: UpdateTemplateDto, 
        req: any, 
        res: Response
    ): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            // Fetch existing template
            const { data: existingTemplate, error: fetchError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('id', templateId)
                .eq('created_by', user.id)
                .single();

            if (fetchError) {
                if (fetchError.code === 'PGRST116') {
                    throw new NotFoundException('Template not found');
                }
                throw new BadRequestException(`Failed to fetch template: ${fetchError.message}`);
            }

            // Attempt Meta integration
            let metaIntegrationSuccess = false;
            const metaCredentials = await this.getMetaCredentials(user.id, existingTemplate.waba_id);
            
            if (metaCredentials) {
                const updatedTemplateData = { ...existingTemplate, ...updateTemplateDto };
                const metaTemplateData = this.metaApiService.convertToMetaFormat(updatedTemplateData);
                
                const operation = existingTemplate.meta_template_id ? 'update' : 'create';
                const metaResult = await this.handleMetaIntegration(
                    existingTemplate,
                    metaCredentials,
                    operation,
                    metaTemplateData
                );

                metaIntegrationSuccess = metaResult.success;

                // Update template with Meta data if successful
                if (metaResult.success && metaResult.response) {
                    updateTemplateDto = {
                        ...updateTemplateDto,
                        meta_template_id: metaResult.response.id,
                        meta_template_status: metaResult.response.status,
                        meta_template_category: metaTemplateData.category
                    } as any;
                }
            }

            // Update template in database
            const { category, ...updateData } = updateTemplateDto;
            const updatePayload = {
                ...updateData,
                updated_at: new Date().toISOString()
            };

            const { data: updatedTemplate, error: updateError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .update(updatePayload)
                .eq('id', templateId)
                .eq('created_by', user.id)
                .select()
                .single();

            if (updateError) {
                throw new BadRequestException(`Failed to update template: ${updateError.message}`);
            }

            const message = metaIntegrationSuccess
                ? 'Template updated successfully with Meta integration'
                : 'Template updated successfully (Meta integration skipped)';

            this.sendResponse(res, this.createSuccessResponse(
                { ...updatedTemplate, meta_integration_success: metaIntegrationSuccess },
                message
            ));

        } catch (error) {
            this.logger.error('Template update failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException || 
                error instanceof NotFoundException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to update template');
        }
    }

    /**
     * Deletes a template
     * @param templateId - Template ID
     * @param wabaId - WhatsApp Business Account ID
     * @param req - Express request
     * @param res - Express response
     */
    async deleteTemplate(templateId: string, wabaId: string, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            // Fetch template to delete
            const { data: template, error: fetchError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('id', templateId)
                .eq('created_by', user.id)
                .single();

            if (fetchError) {
                if (fetchError.code === 'PGRST116') {
                    throw new NotFoundException('Template not found');
                }
                throw new BadRequestException(`Failed to fetch template: ${fetchError.message}`);
            }

            // Attempt Meta deletion
            let metaDeleteSuccess = false;
            if (template.meta_template_id) {
                const metaCredentials = await this.getMetaCredentials(user.id, wabaId);
                
                if (metaCredentials) {
                    try {
                        await this.metaApiService.deleteTemplate(
                            template.meta_template_id,
                            wabaId,
                            metaCredentials.access_token
                        );
                        metaDeleteSuccess = true;
                        this.logger.log(`Template deleted from Meta: ${template.meta_template_id}`);
                    } catch (metaError) {
                        this.logger.error('Failed to delete template from Meta:', metaError.message);
                    }
                }
            }

            // Delete from local database
            const { error: deleteError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .delete()
                .eq('id', templateId)
                .eq('created_by', user.id);

            if (deleteError) {
                throw new BadRequestException(`Failed to delete template: ${deleteError.message}`);
            }

            const message = metaDeleteSuccess
                ? 'Template deleted successfully from both local and Meta'
                : 'Template deleted successfully from local database';

            this.sendResponse(res, this.createSuccessResponse(
                { 
                    deleted: true, 
                    meta_delete_success: metaDeleteSuccess,
                    template_id: templateId
                },
                message
            ));

        } catch (error) {
            this.logger.error('Template deletion failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException || 
                error instanceof NotFoundException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to delete template');
        }
    }

    // ==================== TEMPLATE RETRIEVAL OPERATIONS ====================

    /**
     * Retrieves templates with filtering and pagination
     * @param req - Express request
     * @param query - Query parameters
     * @param res - Express response
     */
    async getTemplates(req: any, query: TemplateQueryDto, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);
            const { page, limit, skip } = this.parsePaginationParams(query);

            // Build base query
            let templatesQuery = this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('created_by', user.id);

            // Apply filters
            templatesQuery = this.applyQueryFilters(templatesQuery, query);

            // Execute paginated query
            const { data: templates, error } = await templatesQuery
                .range(skip, skip + limit - 1)
                .order('created_at', { ascending: false });

            if (error) {
                throw new BadRequestException(`Failed to fetch templates: ${error.message}`);
            }

            // Get total count for pagination
            let countQuery = this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('id', { count: 'exact', head: true })
                .eq('created_by', user.id);

            countQuery = this.applyQueryFilters(countQuery, query);
            const { count, error: countError } = await countQuery;

            if (countError) {
                this.logger.warn('Failed to get templates count:', countError);
            }

            const totalPages = Math.ceil((count || 0) / limit);

            this.sendResponse(res, this.createSuccessResponse({
                templates: templates || [],
                pagination: {
                    page,
                    limit,
                    total: count || 0,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }, 'Templates retrieved successfully'));

        } catch (error) {
            this.logger.error('Templates retrieval failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to retrieve templates');
        }
    }

    /**
     * Retrieves templates by workspace
     * @param workspaceId - Workspace ID
     * @param req - Express request
     * @param query - Query parameters
     * @param res - Express response
     */
    async getTemplatesByWorkspace(
        workspaceId: string, 
        req: any, 
        query: TemplateQueryDto, 
        res: Response
    ): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            // Build workspace-specific query
            let templatesQuery = this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('workspace_id', workspaceId);

            // Apply filters
            templatesQuery = this.applyQueryFilters(templatesQuery, query);

            const { data: templates, error } = await templatesQuery
                .order('created_at', { ascending: false });

            if (error) {
                throw new BadRequestException(`Failed to fetch workspace templates: ${error.message}`);
            }

            this.sendResponse(res, this.createSuccessResponse(
                templates || [],
                'Workspace templates retrieved successfully'
            ));

        } catch (error) {
            this.logger.error('Workspace templates retrieval failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to retrieve workspace templates');
        }
    }

    /**
     * Retrieves a single template by ID
     * @param templateId - Template ID
     * @param req - Express request
     * @param res - Express response
     */
    async getTemplateById(templateId: string, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            const { data: template, error } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('id', templateId)
                .eq('created_by', user.id)
                .single();

            if (error) {
                if (error.code === 'PGRST116') {
                    throw new NotFoundException('Template not found');
                }
                throw new BadRequestException(`Failed to fetch template: ${error.message}`);
            }

            this.sendResponse(res, this.createSuccessResponse(
                template,
                'Template retrieved successfully'
            ));

        } catch (error) {
            this.logger.error('Template retrieval failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException || 
                error instanceof NotFoundException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to retrieve template');
        }
    }

    // ==================== META INTEGRATION OPERATIONS ====================

    /**
     * Syncs a single template with Meta
     * @param templateId - Template ID
     * @param req - Express request
     * @param res - Express response
     */
    async syncWithMeta(templateId: string, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            // Fetch template
            const { data: template, error: fetchError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('id', templateId)
                .eq('created_by', user.id)
                .single();

            if (fetchError) {
                if (fetchError.code === 'PGRST116') {
                    throw new NotFoundException('Template not found');
                }
                throw new BadRequestException(`Failed to fetch template: ${fetchError.message}`);
            }

            // Get Meta credentials
            const metaCredentials = await this.getMetaCredentials(user.id, template.waba_id);
            if (!metaCredentials) {
                throw new BadRequestException('Meta credentials not found for this WABA');
            }

            // Perform Meta sync
            const metaTemplateData = this.metaApiService.convertToMetaFormat(template);
            const operation = template.meta_template_id ? 'update' : 'create';
            
            const metaResult = await this.handleMetaIntegration(
                template,
                metaCredentials,
                operation,
                metaTemplateData
            );

            if (!metaResult.success) {
                throw new BadRequestException(`Meta sync failed: ${metaResult.error}`);
            }

            // Update local template with Meta data
            await this.updateTemplateWithMetaData(templateId, metaResult, metaTemplateData);

            // Fetch updated template
            const { data: updatedTemplate } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('id', templateId)
                .single();

            this.sendResponse(res, this.createSuccessResponse(
                updatedTemplate,
                'Template synced successfully with Meta'
            ));

        } catch (error) {
            this.logger.error('Template Meta sync failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException || 
                error instanceof NotFoundException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to sync template with Meta');
        }
    }

    /**
     * Syncs all templates for a WABA with Meta
     * @param wabaId - WhatsApp Business Account ID
     * @param req - Express request
     * @param res - Express response
     */
    async syncAllWithMeta(wabaId: string, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            // Get all templates for WABA
            const { data: templates, error: fetchError } = await this.supabaseService.getClient()
                .from('automate_whatsapp_templates')
                .select('*')
                .eq('created_by', user.id)
                .eq('waba_id', wabaId);

            if (fetchError) {
                throw new BadRequestException(`Failed to fetch templates: ${fetchError.message}`);
            }

            if (!templates?.length) {
                return this.sendResponse(res, this.createSuccessResponse(
                    [],
                    'No templates found for this WABA'
                ));
            }

            // Get Meta credentials
            const metaCredentials = await this.getMetaCredentials(user.id, wabaId);
            if (!metaCredentials) {
                throw new BadRequestException('Meta credentials not found for this WABA');
            }

            // Sync each template
            const syncResults = await Promise.allSettled(
                templates.map(async (template) => {
                    try {
                        const metaTemplateData = this.metaApiService.convertToMetaFormat(template);
                        const operation = template.meta_template_id ? 'update' : 'create';
                        
                        const metaResult = await this.handleMetaIntegration(
                            template,
                            metaCredentials,
                            operation,
                            metaTemplateData
                        );

                        if (metaResult.success) {
                            await this.updateTemplateWithMetaData(template.id, metaResult, metaTemplateData);
                        }

                        return {
                            templateId: template.id,
                            name: template.name,
                            status: metaResult.success ? 'success' : 'failed',
                            error: metaResult.success ? null : metaResult.error,
                            metaTemplateId: metaResult.response?.id || template.meta_template_id
                        };
                    } catch (error) {
                        return {
                            templateId: template.id,
                            name: template.name,
                            status: 'failed',
                            error: error.message
                        };
                    }
                })
            );

            const results = syncResults.map(result => 
                result.status === 'fulfilled' ? result.value : {
                    status: 'failed',
                    error: result.reason?.message || 'Unknown error'
                }
            );

            const successCount = results.filter(r => r.status === 'success').length;
            const message = `Bulk sync completed: ${successCount}/${templates.length} templates synced successfully`;

            this.sendResponse(res, this.createSuccessResponse(results, message));

        } catch (error) {
            this.logger.error('Bulk Meta sync failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to sync templates with Meta');
        }
    }

    /**
     * Retrieves templates from Meta API
     * @param req - Express request
     * @param res - Express response
     */
    async getMetaTemplates(req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            // This method would need to be implemented based on Meta API capabilities
            // For now, return empty array as placeholder
            this.sendResponse(res, this.createSuccessResponse(
                [],
                'Meta templates retrieved successfully'
            ));

        } catch (error) {
            this.logger.error('Meta templates retrieval failed:', error.message);
            
            if (error instanceof UnauthorizedException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to retrieve Meta templates');
        }
    }

    // ==================== AI INTEGRATION OPERATIONS ====================

    /**
     * Generates template using AI
     * @param aiTemplateDto - AI template generation parameters
     * @param req - Express request
     * @param res - Express response
     */
    async generateAiTemplate(aiTemplateDto: CreateAiTemplateDto, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            // Generate template using AI service
            const aiTemplate = await this.aiService.generateTemplate(aiTemplateDto);

            this.sendResponse(res, this.createSuccessResponse(
                aiTemplate,
                'AI template generated successfully'
            ));

        } catch (error) {
            this.logger.error('AI template generation failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to generate AI template');
        }
    }

    /**
     * Generates template from voice input
     * @param file - Audio file
     * @param body - Request body
     * @param req - Express request
     * @param res - Express response
     */
    async generateTemplateFromVoice(file: any, body: any, req: any, res: Response): Promise<void> {
        try {
            const user = this.validateUserContext(req);

            if (!file) {
                return this.sendResponse(res, this.createErrorResponse(
                    'Audio file is required'
                ));
            }

            const wabaId = body?.waba_id;
            if (!wabaId) {
                return this.sendResponse(res, this.createErrorResponse(
                    'WABA ID is required'
                ));
            }

            this.logger.log(`Processing voice template generation for WABA: ${wabaId}`);

            // For now, return a placeholder response as the AI service method may not exist
            const voiceTemplate = {
                name: 'voice_generated_template',
                content: 'Template generated from voice input',
                type: 'text',
                category: 'UTILITY',
                waba_id: wabaId
            };

            this.sendResponse(res, this.createSuccessResponse(
                voiceTemplate,
                'Template generated from voice successfully'
            ));

        } catch (error) {
            this.logger.error('Voice template generation failed:', error.message);
            
            if (error instanceof UnauthorizedException || 
                error instanceof BadRequestException) {
                throw error;
            }
            
            throw new InternalServerErrorException('Failed to generate template from voice');
        }
    }
}
