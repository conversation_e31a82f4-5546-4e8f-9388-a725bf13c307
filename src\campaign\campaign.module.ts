import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CampaignController } from './campaign.controller';
import { CampaignService } from './campaign.service';
import { CampaignProcessorService } from './campaign-processor.service';
import { Campaign, CampaignSchema } from 'src/schema/campaign.schema';
import { Contact, ContactSchema } from 'src/schema/contacts.schema';
import { CampaignExecution, CampaignExecutionSchema } from 'src/schema/campaign-execution.schema';
import { SupabaseModule } from 'src/supabase/supabase.module';
import { MetaApiModule } from 'src/meta-api/meta-api.module';
import { TemplateModule } from 'src/template/template.module';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from 'src/auth/auth.module';
import { KafkaModule } from '../kafka/kafka.module';

@Module({
  imports: [
    ConfigModule,
    AuthModule,
    SupabaseModule,
    MetaApiModule,
    TemplateModule,
    KafkaModule,
    MongooseModule.forFeature([
      { name: Campaign.name, schema: CampaignSchema },
      { name: Contact.name, schema: ContactSchema },
      { name: CampaignExecution.name, schema: CampaignExecutionSchema }
    ]),
  ],
  controllers: [CampaignController],
  providers: [CampaignService, CampaignProcessorService],
  exports: [CampaignService, CampaignProcessorService],
})
export class CampaignModule {}
