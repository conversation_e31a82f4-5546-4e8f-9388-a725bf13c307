import { Body, Controller, Delete, Get, Param, Post, Put, Req, UseGuards } from '@nestjs/common';
import { CustomFieldsService } from './custom-fields.service';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { AuthGuard } from '../auth/auth.guard';

@Controller('custom-fields')
export class CustomFieldsController {
    constructor(private readonly customFieldsService: CustomFieldsService) {}

    @Post()
    @UseGuards(AuthGuard)
    async create(@Body() dto: CreateCustomFieldDto, @Req() req: any) {
        const result = await this.customFieldsService.create(req.user.id, dto);
        return {
            status: 'success',
            code: 201,
            message: 'Custom field created',
            data: result,
            timestamp: new Date().toISOString(),
        };
    }

    @Get()
    @UseGuards(AuthGuard)
    async findAll(@Req() req: any) {
        const result = await this.customFieldsService.findAll(req.user.id);
        return {
            status: 'success',
            code: 200,
            message: 'Custom fields fetched',
            data: result,
            timestamp: new Date().toISOString(),
        };
    }

    @Get(':id')
    @UseGuards(AuthGuard)
    async findOne(@Param('id') id: string, @Req() req: any) {
        const result = await this.customFieldsService.findOne(req.user.id, id);
        return {
            status: 'success',
            code: 200,
            message: 'Custom field fetched',
            data: result,
            timestamp: new Date().toISOString(),
        };
    }

    @Put(':id')
    @UseGuards(AuthGuard)
    async update(@Param('id') id: string, @Body() dto: UpdateCustomFieldDto, @Req() req: any) {
        const result = await this.customFieldsService.update(req.user.id, id, dto);
        return {
            status: 'success',
            code: 200,
            message: 'Custom field updated',
            data: result,
            timestamp: new Date().toISOString(),
        };
    }

    @Delete(':id')
    @UseGuards(AuthGuard)
    async remove(@Param('id') id: string, @Req() req: any) {
        const result = await this.customFieldsService.remove(req.user.id, id);
        return {
            status: 'success',
            code: 200,
            message: result.message,
            timestamp: new Date().toISOString(),
        };
    }
}


