import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient, User, AuthResponse } from '@supabase/supabase-js';

export interface AuthUser {
  id: string;
  email: string;
  phone?: string;
  user_metadata?: {
    first_name?: string;
    last_name?: string;
    phoneNumber?: string;
    countrycode?: string;
    country?: string;
  };
}

@Injectable()
export class SupabaseService {
  private readonly logger = new Logger(SupabaseService.name);
  private supabase: SupabaseClient;

  constructor(private configService: ConfigService) {
    const supabaseUrl = this.configService.get<string>('SUPABASE_URL');
    const supabaseKey = this.configService.get<string>('SUPABASE_ANON_KEY');

    if (!supabaseUrl || !supabaseKey) {
      this.logger.error('Supabase configuration missing. Please set SUPABASE_URL and SUPABASE_ANON_KEY');
      throw new Error('Supabase configuration missing');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  async signUp(email: string, password: string, userData: {
    first_name: string;
    last_name: string;
    phoneNumber?: string;
    countrycode?: string;
    country?: string;
  }): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            phoneNumber: userData.phoneNumber,
            countrycode: userData.countrycode,
            country: userData.country,
          },
        },
      });

      if (error) {
        this.logger.error('Sign up error:', error);
        throw error;
      }

      return { data, error: null };
    } catch (error) {
      this.logger.error('Sign up failed:', error);
      throw error;
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        this.logger.error('Sign in error:', error);
        throw error;
      }

      return { data, error: null };
    } catch (error) {
      this.logger.error('Sign in failed:', error);
      throw error;
    }
  }

  async refreshSession(refreshToken: string): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (error) {
        this.logger.error('Refresh session error:', error);
        throw error;
      }

      return { data, error: null };
    } catch (error) {
      this.logger.error('Refresh session failed:', error);
      throw error;
    }
  }

  async signOut(): Promise<{ error: any }> {
    try {
      const { error } = await this.supabase.auth.signOut();
      return { error };
    } catch (error) {
      this.logger.error('Sign out failed:', error);
      throw error;
    }
  }

  async resetPassword(email: string, redirectUrl: string): Promise<{ error: any }> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });
      return { error };
    } catch (error) {
      this.logger.error('Password reset failed:', error);
      throw error;
    }
  }

  async getUser(token: string): Promise<User | null> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser(token);
      
      if (error) {
        this.logger.error('Get user error:', error);
        return null;
      }

      return user;
    } catch (error) {
      this.logger.error('Get user failed:', error);
      return null;
    }
  }

  async updateUser(userId: string, updates: {
    email?: string;
    phone?: string;
    user_metadata?: any;
  }): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase.auth.admin.updateUserById(userId, updates);
      return { data, error };
    } catch (error) {
      this.logger.error('Update user failed:', error);
      throw error;
    }
  }

  async updateUserPassword(userId: string, newPassword: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase.auth.admin.updateUserById(userId, {
        password: newPassword,
      } as any);
      return { data, error };
    } catch (error) {
      this.logger.error('Update user password failed:', error);
      throw error;
    }
  }

  async deleteUser(userId: string): Promise<{ error: any }> {
    try {
      const { error } = await this.supabase.auth.admin.deleteUser(userId);
      return { error };
    } catch (error) {
      this.logger.error('Delete user failed:', error);
      throw error;
    }
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  async getUserProfileByEmail(email: string): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('user_profile')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      if (error) {
        this.logger.error('Get user profile error:', error);
        return null;
      }

      return data;
    } catch (error) {
      this.logger.error('Get user profile failed:', error);
      return null;
    }
  }

  async insertUserProfile(profileData: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: number;
    country: string;
    country_code: string;
    terms_conditions: boolean;
  }): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('user_profile')
        .insert(profileData)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Insert user profile failed:', error);
      return { data: null, error };
    }
  }

  async updateUserProfile(userId: string, updates: any): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('user_profile')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Update user profile failed:', error);
      return { data: null, error };
    }
  }

  async getUserProfile(userId: string): Promise<any> {
    console.log(userId);
    try {
      const { data, error } = await this.supabase
        .from('user_profile')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      

      return {data,error}
    } catch (error) {
      this.logger.error('Get user profile failed:', error);
      return null;
    }
  }

  async deleteUserProfile(userId: string): Promise<{ error: any }> {
    try {
      const { error } = await this.supabase
        .from('user_profile')
        .delete()
        .eq('id', userId);

      return { error };
    } catch (error) {
      this.logger.error('Delete user profile failed:', error);
      return { error };
    }
  }

  // Workspace Table Methods
  async insertWorkspace(workspaceData: any): Promise<{ data: any; error: any }> {
    console.log("workspaceData", workspaceData);
    try {
      const { data, error } = await this.supabase
        .from('workspaces')
        .insert(workspaceData)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Insert workspace failed:', error);
      return { data: null, error };
    }
  }
  async getWorkspaceById(id: number): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('workspaces')
        .select('*')
        .eq('id', id)
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Get workspace by ID failed:', error);
      return { data: null, error };
    }
  }

  async getWorkspaceByCreatedBy(createdBy: string): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('workspaces')
        .select('*')
        .eq('created_by', createdBy)
        .maybeSingle();

      if (error) {
        this.logger.error('Get workspace by created by error:', error);
        return null;
      }

      return data;
    } catch (error) {
      this.logger.error('Get workspace by created by failed:', error);
      return null;
    }
  }

  async updateWorkspace(id: number, updates: any): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('workspaces')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Update workspace failed:', error);
      return { data: null, error };
    }
  }

  async deleteWorkspace(id: number): Promise<{ error: any }> {
    try {
      const { error } = await this.supabase
        .from('workspaces')
        .delete()
        .eq('id', id);

      return { error };
    } catch (error) {
      this.logger.error('Delete workspace failed:', error);
      return { error };
    }
  }

  // Workspace Members Table Methods
  async insertWorkspaceMember(memberData: {
    workspace_id: number;
    user_id: string;
    role: string;
    status: string;
    waba_access?: boolean;
    cards_access?: boolean;
  }): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('workspace_members')
        .insert({
          ...memberData,
          created_at: new Date().toISOString(),
          waba_access: memberData.waba_access ?? false,
          cards_access: memberData.cards_access ?? false
        })
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Insert workspace member failed:', error);
      return { data: null, error };
    }
  }

  async getWorkspaceMember(workspaceId: number, userId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('workspace_members')
        .select('*')
        .eq('workspace_id', workspaceId)
        .eq('user_id', userId)
        .maybeSingle();

      return { data, error };
    } catch (error) {
      this.logger.error('Get workspace member failed:', error);
      return { data: null, error };
    }
  }

  // Create admin role with permissions
  async createAdminRoleWithPermissions(workspaceId: number): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .rpc('automate_whatsapp_create_admin_role_with_permissions', {
          workspace_id_param: workspaceId
        })
        .select();

      return { data, error };
    } catch (error) {
      this.logger.error('Create admin role failed:', error);
      return { data: null, error };
    }
  }

  // Insert automate whatsapp member
  async insertAutomateWhatsappMember(memberData: {
    workspace_member_id: number;
    role_id: string;
    user_id: string;
    workspace_id: number;
    status?: string;
  }): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_whatsapp_members')
        .insert({
          ...memberData,
          status: memberData.status ?? 'active',
          joined_at: new Date().toISOString()
        })
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Insert automate whatsapp member failed:', error);
      return { data: null, error };
    }
  }

  // Get automate whatsapp member by workspace_id
  async getAutomateWhatsappMember(user_id: number): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_whatsapp_members')
        .select('role_id,automate_whatsapp_role(name)')
        .eq('user_id', user_id)
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Get automate whatsapp member failed:', error);
      return { data: null, error };
    }
  }

  // Company Profile Table Methods
  async insertCompanyProfile(companyProfileData: any): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_company_profile')
        .insert({
          ...companyProfileData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          social_links: companyProfileData.social_links || []
        })
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Insert company profile failed:', error);
      return { data: null, error };
    }
  }

  async getCompanyProfileById(id: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_company_profile')
        .select('*')
        .eq('id', id)
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Get company profile by ID failed:', error);
      return { data: null, error };
    }
  }

  async getCompanyProfileByWorkspaceId(workspaceId: number): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_company_profile')
        .select('*')
        .eq('workspace_id', workspaceId)
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      this.logger.error('Get company profile by workspace ID failed:', error);
      return { data: null, error };
    }
  }

  async getCompanyProfileByWorkspaceIdWithSearch(
    workspaceId: number,
    search: string = '',
    limit: number = 10,
    offset: number = 0,
    sortBy: string = 'created_at',
    sortOrder: string = 'desc'
  ): Promise<{ data: any; error: any; count: number }> {
    try {
      let query = this.supabase
        .from('automate_card_company_profile')
        .select('*', { count: 'exact' })
        .eq('workspace_id', workspaceId);

      // Add search functionality
      if (search && search.trim() !== '') {
        query = query.or(`company_name.ilike.%${search}%,industry_type.ilike.%${search}%,email.ilike.%${search}%`);
      }

      // Add sorting
      const ascending = sortOrder.toLowerCase() === 'asc';
      query = query.order(sortBy, { ascending });

      // Add pagination
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      return { data, error, count: count || 0 };
    } catch (error) {
      this.logger.error('Get company profile by workspace ID with search failed:', error);
      return { data: null, error, count: 0 };
    }
  }

  async updateCompanyProfile(id: string, updates: any): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_company_profile')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Update company profile failed:', error);
      return { data: null, error };
    }
  }

  async deleteCompanyProfile(id: string): Promise<{ error: any }> {
    try {
      const { error } = await this.supabase
        .from('automate_card_company_profile')
        .delete()
        .eq('id', id);

      return { error };
    } catch (error) {
      this.logger.error('Delete company profile failed:', error);
      return { error };
    }
  }

  // User Profile Card Table Methods
  async insertUserProfileCard(userProfileData: any): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_user_profile')
        .insert({
          ...userProfileData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          social_links: userProfileData.social_links || []
        })
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Insert user profile card failed:', error);
      return { data: null, error };
    }
  }

  async getUserProfileCardById(id: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_user_profile')
        .select(`
          *,
          company_profile:automate_card_company_profile!company_profile_id(
            id,
            company_name,
            company_logo,
            address,
            phone_number,
            country_code,
            email,
            website,
            industry_type
          )
        `)
        .eq('id', id)
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Get user profile card by ID failed:', error);
      return { data: null, error };
    }
  }

  async getUserProfileCardByCompanyAndUser(companyProfileId: string, userId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_user_profile')
        .select(`
          *,
          company_profile:automate_card_company_profile!company_profile_id(
            id,
            company_name,
            company_logo,
            address,
            phone_number,
            country_code,
            email,
            website,
            industry_type
          )
        `)
        .eq('company_profile_id', companyProfileId)
        .eq('user_id', userId)
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Get user profile card by company and user failed:', error);
      return { data: null, error };
    }
  }

  async getUserProfileCardsByCompany(companyProfileId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_user_profile')
        .select(`
          *,
          company_profile:automate_card_company_profile!company_profile_id(
            id,
            company_name,
            company_logo,
            address,
            phone_number,
            country_code,
            email,
            website,
            industry_type
          )
        `)
        .eq('company_profile_id', companyProfileId);

      return { data, error };
    } catch (error) {
      this.logger.error('Get user profile cards by company failed:', error);
      return { data: null, error };
    }
  }

  async getUserProfileCardsByWorkspaceWithSearch(
    workspaceId: number,
    userId: string,
    search: string = '',
    limit: number = 10,
    offset: number = 0,
    sortBy: string = 'created_at',
    sortOrder: string = 'desc'
  ): Promise<{ data: any; error: any; count: number }> {
    try {
      let query = this.supabase
        .from('automate_card_user_profile')
        .select(`
          *,
          company_profile:automate_card_company_profile!company_profile_id(
            id,
            company_name,
            company_logo,
            address,
            phone_number,
            country_code,
            email,
            website,
            industry_type
          )
        `, { count: 'exact' })
        .eq('user_id', userId)
        .eq('company_profile.workspace_id', workspaceId);

      // Add search functionality
      if (search && search.trim() !== '') {
        query = query.or(`full_name.ilike.%${search}%,designation.ilike.%${search}%,email.ilike.%${search}%`);
      }

      // Add sorting
      const ascending = sortOrder.toLowerCase() === 'asc';
      query = query.order(sortBy, { ascending });

      // Add pagination
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      return { data, error, count: count || 0 };
    } catch (error) {
      this.logger.error('Get user profile cards by workspace with search failed:', error);
      return { data: null, error, count: 0 };
    }
  }

  async getUserProfileCardsByCompanyWithSearch(
    companyProfileId: string,
    search: string = '',
    limit: number = 10,
    offset: number = 0,
    sortBy: string = 'created_at',
    sortOrder: string = 'desc'
  ): Promise<{ data: any; error: any; count: number }> {
    try {
      let query = this.supabase
        .from('automate_card_user_profile')
        .select(`
          *,
          company_profile:automate_card_company_profile!company_profile_id(
            id,
            company_name,
            company_logo,
            address,
            phone_number,
            country_code,
            email,
            website,
            industry_type
          )
        `, { count: 'exact' })
        .eq('company_profile_id', companyProfileId);

      // Add search functionality
      if (search && search.trim() !== '') {
        query = query.or(`full_name.ilike.%${search}%,designation.ilike.%${search}%,email.ilike.%${search}%`);
      }

      // Add sorting
      const ascending = sortOrder.toLowerCase() === 'asc';
      query = query.order(sortBy, { ascending });

      // Add pagination
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      return { data, error, count: count || 0 };
    } catch (error) {
      this.logger.error('Get user profile cards by company with search failed:', error);
      return { data: null, error, count: 0 };
    }
  }

  async updateUserProfileCard(id: string, updates: any): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('automate_card_user_profile')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Update user profile card failed:', error);
      return { data: null, error };
    }
  }

  async deleteUserProfileCard(id: string): Promise<{ error: any }> {
    try {
      const { error } = await this.supabase
        .from('automate_card_user_profile')
        .delete()
        .eq('id', id);

      return { error };
    } catch (error) {
      this.logger.error('Delete user profile card failed:', error);
      return { error };
    }
  }

  // Get workspace members for a specific workspace
  async getWorkspaceMembers(workspaceId: number): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('workspace_members')
        .select(`
          *,
          user_profile:user_profile(*)
        `)
        .eq('workspace_id', workspaceId);

      return { data, error };
    } catch (error) {
      this.logger.error('Get workspace members failed:', error);
      return { data: null, error };
    }
  }

  // Get workspace members with search and pagination
  async getWorkspaceMembersWithSearch(
    workspaceId: number,
    search: string = '',
    limit: number = 10,
    offset: number = 0,
    sortBy: string = 'created_at',
    sortOrder: string = 'desc'
  ): Promise<{ data: any; error: any; count: number }> {
    try {
      let query = this.supabase
        .from('workspace_members')
        .select(`
          *,
          user_profile:user_profile(*)
        `, { count: 'exact' })
        .eq('workspace_id', workspaceId);

      // Add search functionality
      if (search && search.trim() !== '') {
        query = query.or(`user_profile.first_name.ilike.%${search}%,user_profile.last_name.ilike.%${search}%,user_profile.email.ilike.%${search}%,role.ilike.%${search}%`);
      }

      // Add sorting
      const ascending = sortOrder.toLowerCase() === 'asc';
      query = query.order(sortBy, { ascending });

      // Add pagination
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      return { data, error, count: count || 0 };
    } catch (error) {
      this.logger.error('Get workspace members with search failed:', error);
      return { data: null, error, count: 0 };
    }
  }

  // Update workspace member access permissions
  async updateWorkspaceMember(
    workspaceId: number, 
    userId: string, 
    updateData: { cards_access?: boolean; waba_access?: boolean }
  ): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from('workspace_members')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('workspace_id', workspaceId)
        .eq('user_id', userId)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      this.logger.error('Update workspace member failed:', error);
      return { data: null, error };
    }
  }

} 