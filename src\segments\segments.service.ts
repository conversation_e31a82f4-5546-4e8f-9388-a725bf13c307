import { BadRequestException, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Segment } from 'src/schema/segment.schema';
import { Contact } from 'src/schema/contacts.schema';
import { SupabaseService } from 'src/supabase/supabase.service';

@Injectable()
export class SegmentsService {
  private readonly logger = new Logger(SegmentsService.name);
  constructor(
    @InjectModel(Segment.name) private segmentModel: Model<Segment>,
    @InjectModel(Contact.name) private contactModel: Model<Contact>,
    private readonly supabaseService: SupabaseService,
  ) {}

  private async getWorkspaceIdOrThrow(userId: string): Promise<number> {
    const { data: userProfile, error } = await this.supabaseService.getUserProfile(userId);
    if (error || !userProfile?.workspace_id) throw new BadRequestException('User not found');
    return userProfile.workspace_id;
  }

  async createSegment(body: { name: string; description?: string; condition?: any; rules?: any[]; match?: 'all'|'any' }, req: any, res: any) {
    const user = req.user;
    if (!user?.id) throw new UnauthorizedException('User not found');
    if (!body?.name) throw new BadRequestException('name is required');
    const workspaceId = await this.getWorkspaceIdOrThrow(user.id);

    // Normalize condition/rules
    let condition: any = undefined;
    if (body.condition) {
      condition = { ...body.condition };
      if (Array.isArray(condition.tagIds)) {
        condition.tagIds = condition.tagIds
          .filter((id: string) => !!id)
          .map((id: string) => new Types.ObjectId(id));
      }
    }
    let rules = Array.isArray(body.rules) ? body.rules : undefined;
    if (rules) {
      rules = rules.map((r: any) => {
        const rule = { field: String(r.field || ''), operator: String(r.operator || ''), value: r.value } as any;
        if ((rule.operator === 'in' || rule.operator === 'hasAnyTag' || rule.operator === 'hasAllTags') && Array.isArray(rule.value)) {
          rule.value = rule.value.map((v: any) => v);
        }
        if ((rule.field === 'tagsId') && (rule.operator === 'hasAnyTag' || rule.operator === 'hasAllTags' || rule.operator === 'in')) {
          if (Array.isArray(rule.value)) {
            rule.value = rule.value.map((id: string) => new Types.ObjectId(id));
          } else if (rule.value) {
            rule.value = [new Types.ObjectId(rule.value)];
          }
        }
        return rule;
      });
    }

    try {
      const segment = await this.segmentModel.create({
        name: body.name.trim(),
        description: body.description,
        condition,
        rules,
        match: body.match || 'all',
        createdBy: user.id,
        workspaceId,
      });
      return res.status(201).json({ status: 'success', code: 201, data: segment });
    } catch (e: any) {
      if (e?.code === 11000) {
        return res.status(409).json({ status: 'error', code: 409, message: 'Segment name already exists in workspace' });
      }
      this.logger.error('Failed to create segment', e);
      throw new BadRequestException('Failed to create segment');
    }
  }

  async listSegments(req: any, res: any) {
    const user = req.user;
    if (!user?.id) throw new UnauthorizedException('User not found');
    const workspaceId = await this.getWorkspaceIdOrThrow(user.id);
    const segments = await this.segmentModel.find({ workspaceId }).sort({ createdAt: -1 }).lean();
    return res.status(200).json({ status: 'success', code: 200, data: segments });
  }

  private buildQueryFromRules(rules: any[], combine: 'all' | 'any', workspaceId: number): any {
    const parts: any[] = [];
    for (const r of rules) {
      const field = String(r.field || '').trim();
      const op = String(r.operator || '').trim();
      const val = r.value;

      if (!field || !op) continue;
      let clause: any = undefined;

      const toRegex = (v: any, flags = 'i') => new RegExp(String(v).replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), flags);

      switch (op) {
        case 'equals':
          clause = { [field]: val };
          break;
        case 'notEquals':
          clause = { [field]: { $ne: val } };
          break;
        case 'contains':
          clause = { [field]: { $regex: toRegex(val) } };
          break;
        case 'notContains':
          clause = { [field]: { $not: toRegex(val) } };
          break;
        case 'startsWith':
          clause = { [field]: { $regex: new RegExp('^' + String(val), 'i') } };
          break;
        case 'endsWith':
          clause = { [field]: { $regex: new RegExp(String(val) + '$', 'i') } };
          break;
        case 'regex':
          clause = { [field]: { $regex: new RegExp(String(val)) } };
          break;
        case 'exists':
          clause = { [field]: { $exists: !!val } };
          break;
        case 'in':
          clause = { [field]: { $in: Array.isArray(val) ? val : [val] } };
          break;
        case 'hasAnyTag':
          clause = { tagsId: { $in: Array.isArray(val) ? val : [val] } };
          break;
        case 'hasAllTags':
          clause = { tagsId: { $all: Array.isArray(val) ? val : [val] } };
          break;
        default:
          break;
      }
      if (clause) parts.push(clause);
    }

    const base = { workspaceId } as any;
    if (parts.length === 0) return base;
    if (combine === 'any') return { ...base, $or: parts };
    return { ...base, $and: parts };
  }

  async getContactsBySegment(segmentId: string, req: any, res: any) {
    const user = req.user;
    if (!user?.id) throw new UnauthorizedException('User not found');
    const workspaceId = await this.getWorkspaceIdOrThrow(user.id);

    const segment = await this.segmentModel.findOne({ _id: segmentId, workspaceId }).lean();
    if (!segment) {
      return res.status(404).json({ status: 'error', code: 404, message: 'Segment not found' });
    }

    // Build contact query from new rules or legacy condition
    let query: any = { workspaceId };
    if (Array.isArray(segment.rules) && segment.rules.length > 0) {
      query = this.buildQueryFromRules(segment.rules as any[], (segment as any).match || 'all', workspaceId);
    } else if (segment.condition) {
      const condition: any = segment.condition || {};
      if (Array.isArray(condition.tagIds) && condition.tagIds.length > 0) {
        query.tagsId = { $in: condition.tagIds };
      }
      if (condition.subscribed !== undefined) {
        query.subscribed = !!condition.subscribed;
      }
      if (condition.nameContains && String(condition.nameContains).trim()) {
        const regex = new RegExp(String(condition.nameContains).trim(), 'i');
        query.$or = [
          { firstName: regex },
          { lastName: regex },
          { chatName: regex },
        ];
      }
      if (condition.emailContains && String(condition.emailContains).trim()) {
        const regex = new RegExp(String(condition.emailContains).trim(), 'i');
        query.email = { $regex: regex };
      }
    }

    const page = parseInt(req.query?.page) > 0 ? parseInt(req.query.page) : 1;
    const limit = parseInt(req.query?.limit) > 0 ? parseInt(req.query.limit) : 10;
    const skip = (page - 1) * limit;

    const [contacts, total] = await Promise.all([
      this.contactModel.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }).lean(),
      this.contactModel.countDocuments(query),
    ]);

    return res.status(200).json({
      status: 'success',
      code: 200,
      data: contacts,
      pagination: { page, limit, total, totalPages: Math.ceil(total / limit) },
    });
  }
}


