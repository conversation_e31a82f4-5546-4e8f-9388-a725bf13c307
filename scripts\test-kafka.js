const { Kafka } = require('kafkajs');

// Kafka configuration
const kafka = new Kafka({
  clientId: 'test-producer',
  brokers: ['13.126.250.70:9092'],
});

const producer = kafka.producer();
const consumer = kafka.consumer({ groupId: 'test-consumer-group' });

async function testKafkaConnection() {
  try {
    console.log('🔄 Connecting to Kafka...');
    
    // Connect producer
    await producer.connect();
    console.log('✅ Producer connected successfully');
    
    // Connect consumer
    await consumer.connect();
    console.log('✅ Consumer connected successfully');
    
    // Subscribe to test topic
    await consumer.subscribe({ topic: 'test-topic', fromBeginning: true });
    console.log('✅ Consumer subscribed to test-topic');
    
    // Send test message
    const testMessage = {
      campaignId: 'test-campaign-123',
      contactId: 'test-contact-456',
      phoneNumber: '+1234567890',
      templateId: 'test-template-789',
      variableMapping: { body: { '1': 'Hello World!' } },
      workspaceId: 1,
      userId: 'test-user-001',
      retryCount: 0,
      priority: 'NORMAL',
    };
    
    await producer.send({
      topic: 'test-topic',
      messages: [
        {
          key: testMessage.campaignId,
          value: JSON.stringify(testMessage),
          headers: {
            'message-type': 'test-message',
            'priority': testMessage.priority,
          },
        },
      ],
    });
    console.log('✅ Test message sent successfully');
    
    // Consume test message
    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        console.log('📨 Received message:');
        console.log('  Topic:', topic);
        console.log('  Partition:', partition);
        console.log('  Key:', message.key.toString());
        console.log('  Value:', message.value.toString());
        console.log('  Headers:', message.headers);
        
        // Disconnect after receiving message
        await consumer.disconnect();
        await producer.disconnect();
        console.log('✅ Test completed successfully');
        process.exit(0);
      },
    });
    
    // Timeout after 10 seconds
    setTimeout(async () => {
      console.log('⏰ Test timeout - no message received');
      await consumer.disconnect();
      await producer.disconnect();
      process.exit(1);
    }, 10000);
    
  } catch (error) {
    console.error('❌ Kafka test failed:', error.message);
    try {
      await consumer.disconnect();
      await producer.disconnect();
    } catch (disconnectError) {
      console.error('Error disconnecting:', disconnectError.message);
    }
    process.exit(1);
  }
}

// Run the test
testKafkaConnection();
