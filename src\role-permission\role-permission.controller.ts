import { RolePermissionService } from './role-permission.service';
import { Controller, UseGuards ,Post, Req, Res} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('role-permission')
export class RolePermissionController {
    constructor(private readonly rolePermissionService: RolePermissionService) {}

    @Post()
    @UseGuards(AuthGuard)
    async createTemplate(@Req() req: any, @Res() res:any) {
        return await this.rolePermissionService.createRolePermission(req, res);
    }
}
