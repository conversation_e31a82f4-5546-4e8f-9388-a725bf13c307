import { Injectable, UnauthorizedException, BadRequestException, NotFoundException } from '@nestjs/common';
import { Response } from 'express';
import { SupabaseService } from 'src/supabase/supabase.service';
import { CreateMetaCredentialsDto, } from 'src/dto/meta-credentials.dto';

@Injectable()
export class MetaOnboardingService {
    constructor(private readonly supabaseService: SupabaseService) {}

    async createMetaCredentials(credentialsDto: CreateMetaCredentialsDto, req: any, res: Response) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Get user profile to get workspace_id
        const userProfile = await this.supabaseService.getUserProfile(user.id);
        if (userProfile.error || !userProfile.data) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: 'User profile not found',
                timestamp: new Date().toISOString()
            });
        }
        const payload = {
            whatsapp_business_id: credentialsDto.whatsapp_business_id,
            phone_number_id: credentialsDto.phone_number_id,
            access_token: credentialsDto.access_token,
            workspace_id: userProfile.data.workspace_id,
            created_by: user.id,
            status: credentialsDto.status || 'Active'
        };

        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .insert(payload)
            .select()
            .single();

        if (error) {
            return res.status(400).json({
                status: 'error',
                code: 400,
                message: `Failed to create credentials: ${error.message}`,
                timestamp: new Date().toISOString()
            });
        }

        return res.status(201).json({
            status: 'success',
            code: 201,
            message: 'Meta credentials created successfully',
            data: data,
            timestamp: new Date().toISOString()
        });
    }

    async getMetaCredentialsByWorkspace(workspaceId: string, req: any, res: Response, skip: number = 0, limit: number = 10) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        // Check if user has access to this workspace
        const workspaceMember = await this.supabaseService.getWorkspaceMember(parseInt(workspaceId), user.id);
        if (workspaceMember.error || !workspaceMember.data) {
            throw new UnauthorizedException('You do not have access to this workspace');
        }

        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('workspace_id', parseInt(workspaceId))
            .range(skip, skip + limit - 1)
            .order('created_at', { ascending: false });

        if (error) {
           throw new BadRequestException(`Failed to fetch credentials: ${error.message}`);
        }

        // Get total count
        const { count, error: countError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*', { count: 'exact', head: true })
            .eq('workspace_id', parseInt(workspaceId));

        if (countError) {
            throw new BadRequestException(`Failed to get count: ${countError.message}`);
        }

        const result = {
            data,
            total: count,
            page: Math.floor(skip / limit) + 1,
            limit
        };
        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'Workspace credentials retrieved successfully',
            data: result,
            timestamp: new Date().toISOString()
        });
    }

    async getMetaCredentialsByUser(req: any, res: Response, skip: number = 0, limit: number = 10) {
        const user = req.user;
        if (!user || !user.id) {
            throw new UnauthorizedException('User not found');
        }

        const { data, error } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*')
            .eq('created_by', user.id)
            .range(skip, skip + limit - 1)
            .order('created_at', { ascending: false });

        if (error) {
            throw new BadRequestException(`Failed to fetch credentials: ${error.message}`);
        }

        // Get total count
        const { count, error: countError } = await this.supabaseService.getClient()
            .from('automate_whatsapp_meta_credentials')
            .select('*', { count: 'exact', head: true })
            .eq('created_by', user.id);

        if (countError) {
           throw new BadRequestException(`Failed to get count: ${countError.message}`);
        }

        const result = {
            data,
            total: count,
            page: Math.floor(skip / limit) + 1,
            limit
        };

        return res.status(200).json({
            status: 'success',
            code: 200,
            message: 'User credentials retrieved successfully',
            data: result,
            timestamp: new Date().toISOString()
        });
    }
}
