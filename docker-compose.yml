version: '3.8'
services:
  redis:
    image: redis:7.4.6-alpine
    container_name: whatsapp-redis
    ports:
      - "6379:6379"
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  app:
    build: .
    container_name: whatsapp-backend
    env_file:
      - .env
    ports:
      - "8000:8000"
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    # Uncomment if you want to mount code for live reload during development
    # volumes:
    #   - .:/usr/src/app

volumes:
  redis-data:
    driver: local
