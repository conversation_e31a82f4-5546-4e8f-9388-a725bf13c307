import { Body, Controller, Get, Param, Post, Req, Res, UseGuards } from '@nestjs/common';
import { SegmentsService } from './segments.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { SEGMENT_OPERATORS } from './operators';

@Controller('segments')
export class SegmentsController {
  constructor(private readonly segmentsService: SegmentsService) {}

  @Post()
  @UseGuards(AuthGuard)
  async create(@Body() body: { name: string; description?: string; condition: any }, @Req() req: any, @Res() res: any) {
    return this.segmentsService.createSegment(body, req, res);
  }

  @Get()
  @UseGuards(AuthGuard)
  async list(@Req() req: any, @Res() res: any) {
    return this.segmentsService.listSegments(req, res);
  }

  @Get(':id/contacts')
  @UseGuards(AuthGuard)
  async contacts(@Param('id') id: string, @Req() req: any, @Res() res: any) {
    return this.segmentsService.getContactsBySegment(id, req, res);
  }

  @Get('operators/list')
  @UseGuards(AuthGuard)
  async operators(@Res() res: any) {
    return res.status(200).json({ status: 'success', code: 200, data: SEGMENT_OPERATORS });
  }
}


